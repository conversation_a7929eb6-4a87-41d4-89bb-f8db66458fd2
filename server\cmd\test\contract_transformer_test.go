package test

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	srv "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insfinance"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

// ContractTransformerTester 合同数据转换测试器
type ContractTransformerTester struct {
	transformer *srv.ContractTransformer
	configMgr   *srv.MappingConfigManager
}

// NewContractTransformerTester 创建测试器实例
func NewContractTransformerTester() *ContractTransformerTester {
	return &ContractTransformerTester{
		transformer: srv.NewContractTransformer(),
		configMgr:   srv.NewMappingConfigManager("./config/contract_mappings"),
	}
}

// TestWithDatabaseData 使用数据库数据进行测试
func (tester *ContractTransformerTester) TestWithDatabaseData(instanceCode string) error {
	prepare()
	ctx, logger := global.Log4Task(context.Background(), "TestWithDatabaseData",
		zap.String("instance_code", instanceCode),
	)

	logger.Info("开始从数据库查询合同数据进行转换测试")

	// 1. 从数据库查询合同数据
	contract, err := tester.queryContractFromDB(instanceCode)
	if err != nil {
		logger.Error("查询合同数据失败", zap.Error(err))
		return fmt.Errorf("查询合同数据失败: %w", err)
	}

	logger.Info("成功查询到合同数据",
		zap.String("approval_code", contract.ApprovalCode),
		zap.String("approval_name", contract.ApprovalName),
		zap.String("status", contract.Status),
	)

	// 2. 加载配置
	if err := tester.loadPaymentApplicationConfig(); err != nil {
		logger.Error("加载配置失败", zap.Error(err))
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 3. 转换为 insfinance.ContractDetails 格式
	contractDetails := tester.convertToContractDetails(contract)

	// 4. 执行数据转换
	result, err := tester.transformer.Transform(ctx, contractDetails)
	if err != nil {
		logger.Error("数据转换失败", zap.Error(err))
		return fmt.Errorf("数据转换失败: %w", err)
	}

	// 5. 输出转换结果
	tester.printDetailedResult(result, contract)

	logger.Info("数据库数据转换测试完成")
	return nil
}

// queryContractFromDB 从数据库查询合同数据
func (tester *ContractTransformerTester) queryContractFromDB(instanceCode string) (*insbuy.InsContract, error) {
	db := query.InsContract

	contract, err := db.WithContext(context.Background()).
		Where(db.InstanceCode.Eq(instanceCode)).
		First()

	if err != nil {
		return nil, fmt.Errorf("查询合同失败: %w", err)
	}

	return contract, nil
}

// loadPaymentApplicationConfig 加载付款申请配置
func (tester *ContractTransformerTester) loadPaymentApplicationConfig() error {
	// 加载付款申请配置文件
	if err := tester.configMgr.LoadFromFile("payment_application.yaml"); err != nil {
		return fmt.Errorf("加载付款申请配置失败: %w", err)
	}

	// 将配置添加到转换器
	rule, exists := tester.configMgr.GetRule("PAYMENT_REQUEST_APPROVAL")

	if !exists {
		return fmt.Errorf("未找到付款申请配置规则")
	}
	rules := tester.transformer.MappingRules()
	rules["PAYMENT_REQUEST_APPROVAL"] = rule
	tester.transformer.SetMappingRules(rules)
	return nil
}

// convertToContractDetails 将数据库模型转换为 ContractDetails
func (tester *ContractTransformerTester) convertToContractDetails(contract *insbuy.InsContract) *insfinance.ContractDetails {
	// 解析表单数据
	var formData []map[string]interface{}
	if contract.Form != nil {
		json.Unmarshal(contract.Form, &formData)
	}

	// 转换表单数据为 widget 格式的 map
	formMap := make(map[string]interface{})
	for _, field := range formData {
		if id, exists := field["id"]; exists {
			formMap[fmt.Sprintf("%v", id)] = field
		}
	}

	// 构建 ContractDetails
	details := &insfinance.ContractDetails{
		ApprovalCode: contract.ApprovalCode, // 使用配置中的审批代码
		ApprovalName: contract.ApprovalName,
		InstanceCode: contract.InstanceCode,
		SerialNumber: contract.SerialNumber,
		Uuid:         contract.Uuid,
		Status:       contract.Status,
		Reverted:     contract.Reverted,
		DepartmentId: contract.DepartmentId,
		OpenId:       contract.OpenId,
		UserId:       contract.UserId,
	}

	// 转换时间
	if contract.StartTime != nil {
		details.StartTime = contract.StartTime.Format(time.DateTime)
	}
	if contract.EndTime != nil {
		details.EndTime = contract.EndTime.Format(time.DateTime)
	}

	// 转换表单数据为JSON字符串
	if len(formMap) > 0 {
		if formJson, err := json.Marshal(formMap); err == nil {
			details.Form = string(formJson)
		}
	} else if contract.Form != nil {
		// 如果没有解析成功，直接使用原始JSON数据
		details.Form = string(contract.Form)
	}

	return details
}

// printDetailedResult 打印详细的转换结果
func (tester *ContractTransformerTester) printDetailedResult(result *srv.TransformResult, originalContract *insbuy.InsContract) {
	logger := global.GVA_LOG

	logger.Info("=== 数据库数据转换测试结果 ===")

	// 原始数据信息
	logger.Info("原始数据库数据:",
		zap.String("instance_code", originalContract.InstanceCode),
		zap.String("approval_name", originalContract.ApprovalName),
		zap.String("status", originalContract.Status),
		zap.Time("created_at", originalContract.CreatedAt),
	)

	// 转换结果
	logger.Info("转换结果:",
		zap.Bool("success", result.Success),
		zap.Duration("processing_time", result.ProcessingTime),
		zap.Int("error_count", len(result.Errors)),
		zap.Int("warning_count", len(result.Warnings)),
	)

	// 错误信息
	if len(result.Errors) > 0 {
		logger.Info("转换错误:")
		for i, err := range result.Errors {
			logger.Info("错误详情",
				zap.Int("index", i+1),
				zap.String("field", err.Field),
				zap.String("message", err.Message),
				zap.String("value", err.Value),
			)
		}
	}

	// 警告信息
	if len(result.Warnings) > 0 {
		logger.Info("转换警告:")
		for i, warning := range result.Warnings {
			logger.Info("警告详情",
				zap.Int("index", i+1),
				zap.String("message", warning),
			)
		}
	}

	// 转换后的标准化数据
	if result.Data != nil {
		data := result.Data
		logger.Info("标准化数据 - 基础信息:",
			zap.String("application_number", data.ApplicationNumber),
			zap.String("title", data.Title),
			zap.String("application_status", data.ApplicationStatus),
			zap.Time("initiate_time", data.InitiateTime),
			zap.Time("complete_time", data.CompleteTime),
		)

		logger.Info("标准化数据 - 人员信息:",
			zap.String("initiator_user_id", data.InitiatorUserId),
			zap.String("initiator_department_id", data.InitiatorDepartmentId),
			zap.String("serial_number", data.SerialNumber),
		)

		logger.Info("标准化数据 - 业务信息:",
			zap.String("payment_reason", data.PaymentReason),
			zap.String("payment_entity", data.PaymentEntity),
			zap.String("business_type", data.BusinessType),
			zap.String("expense_department", data.ExpenseDepartment),
			zap.String("payment_currency", data.PaymentCurrency),
		)

		logger.Info("标准化数据 - 金额信息:",
			zap.Float64("contract_sign_amount", data.ContractSignAmount),
			zap.Float64("contract_paid_amount", data.ContractPaidAmount),
			zap.Float64("current_request_amount", data.CurrentRequestAmount),
		)

		logger.Info("标准化数据 - 财务信息:",
			zap.String("vat_invoice_type", data.VatInvoiceType),
			zap.Float64("tax_rate", data.TaxRate),
		)

		logger.Info("标准化数据 - 银行信息:",
			zap.String("account_holder", data.AccountHolder),
			zap.String("account_type", data.AccountType),
			zap.String("account_number", data.AccountNumber),
			zap.String("bank_name", data.BankName),
			zap.String("bank_branch", data.BankBranch),
			zap.String("bank_region", data.BankRegion),
		)

		logger.Info("标准化数据 - 其他信息:",
			zap.Time("expected_payment_date", data.ExpectedPaymentDate),
			zap.Any("attachments", data.Attachments),
			zap.String("remarks", data.Remarks),
		)

		logger.Info("标准化数据 - 元数据:",
			zap.String("source_instance_code", data.SourceInstanceCode),
			zap.String("approval_code", data.ApprovalCode),
			zap.Time("transform_time", data.TransformTime),
			zap.String("data_version", data.DataVersion),
		)
	}

	logger.Info("=== 转换测试结果结束 ===")
}

// TestBatchTransform 批量转换测试
func (tester *ContractTransformerTester) TestBatchTransform(limit int) error {
	ctx, logger := global.Log4Task(context.Background(), "TestBatchTransform",
		zap.Int("limit", limit),
	)

	logger.Info("开始批量转换测试")

	// 1. 查询多条合同数据
	contracts, err := tester.queryMultipleContracts(limit)
	if err != nil {
		logger.Error("查询多条合同数据失败", zap.Error(err))
		return fmt.Errorf("查询多条合同数据失败: %w", err)
	}

	logger.Info("查询到合同数据", zap.Int("count", len(contracts)))

	// 2. 加载配置
	if err := tester.loadPaymentApplicationConfig(); err != nil {
		logger.Error("加载配置失败", zap.Error(err))
		return fmt.Errorf("加载配置失败: %w", err)
	}

	// 3. 批量转换
	successCount := 0
	failCount := 0

	for i, contract := range contracts {
		logger.Info("处理合同",
			zap.Int("index", i+1),
			zap.String("instance_code", contract.InstanceCode),
		)

		// 转换为 ContractDetails
		contractDetails := tester.convertToContractDetails(contract)

		// 执行转换
		result, err := tester.transformer.Transform(ctx, contractDetails)
		if err != nil {
			logger.Error("转换失败",
				zap.String("instance_code", contract.InstanceCode),
				zap.Error(err),
			)
			failCount++
			continue
		}

		if result.Success {
			successCount++
			logger.Info("转换成功",
				zap.String("instance_code", contract.InstanceCode),
				zap.Duration("processing_time", result.ProcessingTime),
			)
		} else {
			failCount++
			logger.Error("转换失败",
				zap.String("instance_code", contract.InstanceCode),
				zap.Int("error_count", len(result.Errors)),
			)
		}
	}

	logger.Info("批量转换测试完成",
		zap.Int("total", len(contracts)),
		zap.Int("success", successCount),
		zap.Int("failed", failCount),
	)

	return nil
}

// queryMultipleContracts 查询多条合同数据
func (tester *ContractTransformerTester) queryMultipleContracts(limit int) ([]*insbuy.InsContract, error) {
	db := query.InsContract

	contracts, err := db.WithContext(context.Background()).
		Where(db.Form.IsNotNull()).
		Limit(limit).
		Find()

	if err != nil {
		return nil, fmt.Errorf("查询多条合同失败: %w", err)
	}

	return contracts, nil
}

// TestPaymentApplicationConfig 测试付款申请配置文件
func TestPaymentApplicationConfig(t *testing.T) {
	prepare()
	tester := NewContractTransformerTester()

	// 测试配置文件加载
	err := tester.configMgr.LoadFromFile("payment_application.yaml")
	if err != nil {
		t.Fatalf("加载付款申请配置文件失败: %v", err)
	}

	// 验证配置规则是否存在
	rule, exists := tester.configMgr.GetRule("F523F053-7AC6-4280-A4E7-B35E0C0431B5")
	if !exists {
		t.Fatal("未找到付款申请配置规则")
	}

	// 验证配置规则的基本信息
	if rule.ApprovalCode != "F523F053-7AC6-4280-A4E7-B35E0C0431B5" {
		t.Errorf("审批代码不匹配，期望: PAYMENT_REQUEST_APPROVAL, 实际: %s", rule.ApprovalCode)
	}

	if rule.ApprovalName != "付款申请审批" {
		t.Errorf("审批名称不匹配，期望: 付款申请审批, 实际: %s", rule.ApprovalName)
	}

	// 验证关键字段映射是否存在
	requiredMappings := []string{
		"payment_reason", "payment_entity", "current_request_amount",
		"account_holder", "account_number", "tax_rate",
	}

	for _, fieldName := range requiredMappings {
		if _, exists := rule.FieldMappings[fieldName]; !exists {
			t.Errorf("缺少必要的字段映射: %s", fieldName)
		}
	}
	for s, v := range rule.FieldMappings {
		t.Logf("字段映射: %s -> %+v", s, v.TargetField)
	}

	t.Logf("付款申请配置验证成功，包含 %d 个字段映射", len(rule.FieldMappings))
}

// TestUniversalDataExport 通用数据导出测试方法
// 该方法能够自动识别审批类型并加载对应的配置，无需硬编码审批代码
func (tester *ContractTransformerTester) TestUniversalDataExport(instanceCode string) error {
	prepare()
	ctx, logger := global.Log4Task(context.Background(), "TestUniversalDataExport",
		zap.String("instance_code", instanceCode),
	)

	logger.Info("开始通用数据导出测试")

	// 1. 从数据库查询合同数据
	contract, err := tester.queryContractFromDB(instanceCode)
	if err != nil {
		logger.Error("查询合同数据失败", zap.Error(err))
		return fmt.Errorf("查询合同数据失败: %w", err)
	}

	if contract == nil {
		return fmt.Errorf("未找到实例代码为 %s 的合同数据", instanceCode)
	}

	logger.Info("成功查询到合同数据",
		zap.String("approval_code", contract.ApprovalCode),
		zap.String("approval_name", contract.ApprovalName),
		zap.String("status", contract.Status),
	)

	// 2. 自动加载所有配置文件
	err = tester.loadAllMappingConfigs()
	if err != nil {
		logger.Error("加载映射配置失败", zap.Error(err))
		return fmt.Errorf("加载映射配置失败: %w", err)
	}

	// 3. 根据合同的审批代码自动选择映射规则
	mappingRule, err := tester.selectMappingRuleByApprovalCode(contract.ApprovalCode)
	if err != nil {
		logger.Error("选择映射规则失败", zap.Error(err))
		return fmt.Errorf("选择映射规则失败: %w", err)
	}

	logger.Info("成功选择映射规则",
		zap.String("rule_approval_code", mappingRule.ApprovalCode),
		zap.String("rule_approval_name", mappingRule.ApprovalName),
		zap.Int("field_mappings_count", len(mappingRule.FieldMappings)),
	)

	// 4. 设置映射规则到转换器
	tester.transformer.SetMappingRules(tester.configMgr.GetAllRules())

	// 5. 转换为 ContractDetails 格式
	contractDetails := tester.convertToContractDetails(contract)

	// 6. 执行数据转换
	result, err := tester.transformer.Transform(ctx, contractDetails)
	if err != nil {
		logger.Error("数据转换失败", zap.Error(err))
		return fmt.Errorf("数据转换失败: %w", err)
	}

	// 7. 检查转换结果
	if !result.Success {
		logger.Warn("转换存在警告或错误",
			zap.Strings("warnings", result.Warnings),
			zap.Any("errors", result.Errors),
		)
	}

	// 8. 生成Excel文件名（包含审批类型和时间戳）
	timestamp := time.Now().Format("20060102_150405")
	approvalType := tester.getApprovalTypeFromCode(contract.ApprovalCode)
	filename := fmt.Sprintf("%s_export_%s_%s.xlsx", approvalType, instanceCode, timestamp)

	// 9. 导出到Excel
	err = tester.ExportToExcel(result.Data, filename)
	if err != nil {
		logger.Error("导出Excel失败", zap.Error(err))
		return fmt.Errorf("导出Excel失败: %w", err)
	}

	// 10. 输出详细的转换结果
	tester.printDetailedResult(result, contract)

	logger.Info("通用数据导出测试完成",
		zap.String("output_file", filename),
		zap.Bool("success", result.Success),
		zap.Duration("processing_time", result.ProcessingTime),
	)

	fmt.Printf("测试完成，结果已导出到: %s\n", filename)
	return nil
}

// loadAllMappingConfigs 加载所有映射配置文件
func (tester *ContractTransformerTester) loadAllMappingConfigs() error {
	ctx, logger := global.Log4Task(context.Background(), "LoadAllMappingConfigs")
	_ = ctx

	logger.Info("开始加载所有映射配置文件")

	// 使用配置管理器的 LoadFromDirectory 方法加载所有配置文件
	err := tester.configMgr.LoadFromDirectory()
	if err != nil {
		logger.Error("加载配置目录失败", zap.Error(err))
		return fmt.Errorf("加载配置目录失败: %w", err)
	}

	// 获取所有已加载的规则
	allRules := tester.configMgr.GetAllRules()
	logger.Info("成功加载映射配置",
		zap.Int("total_rules", len(allRules)),
	)

	// 记录每个加载的规则
	for approvalCode, rule := range allRules {
		logger.Info("已加载映射规则",
			zap.String("approval_code", approvalCode),
			zap.String("approval_name", rule.ApprovalName),
			zap.Int("field_mappings", len(rule.FieldMappings)),
		)
	}

	return nil
}

// selectMappingRuleByApprovalCode 根据审批代码自动选择映射规则
func (tester *ContractTransformerTester) selectMappingRuleByApprovalCode(approvalCode string) (*srv.FieldMappingRule, error) {
	ctx, logger := global.Log4Task(context.Background(), "SelectMappingRule",
		zap.String("approval_code", approvalCode),
	)
	_ = ctx

	logger.Info("开始选择映射规则")

	// 1. 首先尝试精确匹配审批代码
	rule, exists := tester.configMgr.GetRule(approvalCode)
	if exists {
		logger.Info("找到精确匹配的映射规则",
			zap.String("approval_code", approvalCode),
			zap.String("rule_name", rule.ApprovalName),
		)
		return rule, nil
	}

	// 2. 如果没有精确匹配，尝试根据审批代码模式匹配
	allRules := tester.configMgr.GetAllRules()

	// 尝试模糊匹配（例如：包含关键词）
	for ruleCode, ruleConfig := range allRules {
		if tester.isApprovalCodeMatch(approvalCode, ruleCode) {
			logger.Info("找到模式匹配的映射规则",
				zap.String("approval_code", approvalCode),
				zap.String("matched_rule_code", ruleCode),
				zap.String("rule_name", ruleConfig.ApprovalName),
			)
			return ruleConfig, nil
		}
	}

	// 3. 如果仍然没有匹配，尝试使用默认规则
	defaultRule, exists := tester.configMgr.GetRule("default")
	if exists {
		logger.Warn("使用默认映射规则",
			zap.String("approval_code", approvalCode),
		)
		return defaultRule, nil
	}

	// 4. 如果没有找到任何匹配的规则，返回错误
	logger.Error("未找到匹配的映射规则",
		zap.String("approval_code", approvalCode),
		zap.Int("available_rules", len(allRules)),
	)

	return nil, fmt.Errorf("未找到审批代码 %s 的映射规则，可用规则: %v",
		approvalCode, tester.getAvailableRuleCodes())
}

// isApprovalCodeMatch 检查审批代码是否匹配
func (tester *ContractTransformerTester) isApprovalCodeMatch(approvalCode, ruleCode string) bool {
	// 实现模糊匹配逻辑
	// 可以根据实际需求扩展匹配规则

	// 1. 检查是否包含关键词
	keywords := map[string][]string{
		"PAYMENT_REQUEST_APPROVAL": {"PAYMENT", "REQUEST", "付款"},
		"CONTRACT_APPROVAL":        {"CONTRACT", "合同"},
		"PURCHASE_APPROVAL":        {"PURCHASE", "采购"},
		"EXPENSE_APPROVAL":         {"EXPENSE", "费用"},
	}

	if keywordList, exists := keywords[ruleCode]; exists {
		for _, keyword := range keywordList {
			if strings.Contains(strings.ToUpper(approvalCode), strings.ToUpper(keyword)) {
				return true
			}
		}
	}

	return false
}

// getApprovalTypeFromCode 从审批代码获取审批类型名称
func (tester *ContractTransformerTester) getApprovalTypeFromCode(approvalCode string) string {
	// 定义审批代码到类型名称的映射
	typeMapping := map[string]string{
		"PAYMENT_REQUEST_APPROVAL": "payment_request",
		"CONTRACT_APPROVAL":        "contract",
		"PURCHASE_APPROVAL":        "purchase",
		"EXPENSE_APPROVAL":         "expense",
	}

	// 首先尝试精确匹配
	if typeName, exists := typeMapping[approvalCode]; exists {
		return typeName
	}

	// 尝试模糊匹配
	upperCode := strings.ToUpper(approvalCode)
	if strings.Contains(upperCode, "PAYMENT") || strings.Contains(upperCode, "付款") {
		return "payment_request"
	}
	if strings.Contains(upperCode, "CONTRACT") || strings.Contains(upperCode, "合同") {
		return "contract"
	}
	if strings.Contains(upperCode, "PURCHASE") || strings.Contains(upperCode, "采购") {
		return "purchase"
	}
	if strings.Contains(upperCode, "EXPENSE") || strings.Contains(upperCode, "费用") {
		return "expense"
	}

	// 如果都不匹配，返回通用名称
	return "approval"
}

// getAvailableRuleCodes 获取所有可用的规则代码
func (tester *ContractTransformerTester) getAvailableRuleCodes() []string {
	allRules := tester.configMgr.GetAllRules()
	codes := make([]string, 0, len(allRules))
	for code := range allRules {
		codes = append(codes, code)
	}
	return codes
}

// TestBatchTransformPaymentApplication 测试批量转换
func TestBatchTransformPaymentApplication(t *testing.T) {
	tester := NewContractTransformerTester()

	err := tester.TestBatchTransform(10) // 测试10条数据
	if err != nil {
		t.Errorf("批量转换测试失败: %v", err)
	}
}

// TestUniversalDataExportWithInstanceCode 使用通用方法测试数据导出
func TestUniversalDataExportWithInstanceCode(t *testing.T) {
	tester := NewContractTransformerTester()

	// 使用具体的实例代码进行测试
	instanceCode := "72ECAA63-24BF-4E52-BE1C-74A64DA51583" // 替换为实际的实例代码

	// 执行通用数据导出测试
	err := tester.TestUniversalDataExport(instanceCode)
	if err != nil {
		t.Errorf("通用数据导出测试失败: %v", err)
	}
}

// TestUniversalDataExportBatch 批量测试通用数据导出
func TestUniversalDataExportBatch(t *testing.T) {
	tester := NewContractTransformerTester()

	// 查询多条合同数据进行批量测试
	contracts, err := tester.queryMultipleContracts(5) // 测试5条数据
	if err != nil {
		t.Fatalf("查询合同数据失败: %v", err)
	}

	successCount := 0
	failCount := 0

	for i, contract := range contracts {
		t.Logf("测试第 %d 条数据，实例代码: %s", i+1, contract.InstanceCode)

		err := tester.TestUniversalDataExport(contract.InstanceCode)
		if err != nil {
			t.Logf("第 %d 条数据导出失败: %v", i+1, err)
			failCount++
		} else {
			t.Logf("第 %d 条数据导出成功", i+1)
			successCount++
		}
	}

	t.Logf("批量测试完成，成功: %d, 失败: %d", successCount, failCount)

	if failCount > 0 {
		t.Errorf("批量测试中有 %d 条数据导出失败", failCount)
	}
}

// ExportToExcel 导出转换结果到Excel文件
func (tester *ContractTransformerTester) ExportToExcel(data *srv.StandardContractData, filename string) error {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Printf("关闭Excel文件失败: %v\n", err)
		}
	}()

	// 创建工作表
	sheetName := "合同数据转换结果"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return fmt.Errorf("创建工作表失败: %w", err)
	}

	// 检查是否有费用明细数据，如果有则需要分裂数据
	hasExpenseDetails := len(data.ExpenseDetails) > 0

	var headers []string
	if hasExpenseDetails {
		// 费用报销表头（包含明细字段）
		headers = []string{
			"申请编号", "标题", "申请状态", "发起时间", "完成时间",
			"发起人工号", "发起人用户ID", "发起人姓名", "发起人部门ID",
			"报销事由", "报销主体", "费用总金额", "业务类型",
			"收款方户名", "账户号码", "账户类型", "银行名称", "银行支行", "银行地区",
			"期望付款日期", "备注", "附件数量",
			// 费用明细字段
			"明细序号", "费用类型", "地点", "日期区间", "开始日期", "结束日期",
			"增值税发票类型", "明细金额", "金额币种", "金额大写",
			"源实例代码", "审批代码", "转换时间", "数据版本",
		}
	} else {
		// 原有的付款申请表头
		headers = []string{
			"申请编号", "标题", "申请状态", "发起时间", "完成时间",
			"发起人工号", "发起人用户ID", "发起人姓名", "发起人部门ID",
			"付款事由", "付款主体", "业务类型", "付款币种",
			"合同签约金额", "合同已付金额", "本次请款金额", "税率", "增值税发票类型",
			"收款方户名", "账户号码", "账户类型", "银行名称", "银行支行", "银行地区",
			"期望付款日期", "备注", "附件数量",
			"源实例代码", "审批代码", "转换时间", "数据版本",
		}
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E6E6FA"},
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		return fmt.Errorf("创建表头样式失败: %w", err)
	}

	// 应用表头样式
	f.SetCellStyle(sheetName, "A1", fmt.Sprintf("%s1", string(rune('A'+len(headers)-1))), headerStyle)

	// 写入数据
	row := 2

	if hasExpenseDetails {
		// 费用报销数据：为每个费用明细创建一行
		for detailIndex, detail := range data.ExpenseDetails {
			values := []interface{}{
				data.ApplicationNumber,
				data.Title,
				data.ApplicationStatus,
				tester.formatTime(data.InitiateTime),
				tester.formatTime(data.CompleteTime),
				data.InitiatorEmployeeId,
				data.InitiatorUserId,
				data.InitiatorName,
				data.InitiatorDepartmentId,
				data.ReimbursementReason, // 报销事由
				data.ReimbursementEntity, // 报销主体
				data.TotalAmount,         // 费用总金额
				data.BusinessType,
				data.AccountHolder,
				data.AccountNumber,
				data.AccountType,
				data.BankName,
				data.BankBranch,
				data.BankRegion,
				tester.formatTime(data.ExpectedPaymentDate),
				data.Remarks,
				len(data.Attachments),
				// 费用明细字段
				detailIndex + 1,                     // 明细序号
				detail.ExpenseType,                  // 费用类型
				detail.Location,                     // 地点
				detail.DateRange,                    // 日期区间
				tester.formatDate(detail.StartDate), // 开始日期
				tester.formatDate(detail.EndDate),   // 结束日期
				detail.VatInvoiceType,               // 增值税发票类型
				detail.Amount,                       // 明细金额
				detail.AmountCurrency,               // 金额币种
				detail.AmountCapital,                // 金额大写
				data.SourceInstanceCode,
				data.ApprovalCode,
				tester.formatTime(data.TransformTime),
				data.DataVersion,
			}

			for i, value := range values {
				cell := fmt.Sprintf("%s%d", string(rune('A'+i)), row)
				f.SetCellValue(sheetName, cell, value)
			}
			row++
		}
	} else {
		// 原有的付款申请数据：单行记录
		values := []interface{}{
			data.ApplicationNumber,
			data.Title,
			data.ApplicationStatus,
			tester.formatTime(data.InitiateTime),
			tester.formatTime(data.CompleteTime),
			data.InitiatorEmployeeId,
			data.InitiatorUserId,
			data.InitiatorName,
			data.InitiatorDepartmentId,
			data.PaymentReason,
			data.PaymentEntity,
			data.BusinessType,
			data.PaymentCurrency,
			data.ContractSignAmount,
			data.ContractPaidAmount,
			data.CurrentRequestAmount,
			data.TaxRate,
			data.VatInvoiceType,
			data.AccountHolder,
			data.AccountNumber,
			data.AccountType,
			data.BankName,
			data.BankBranch,
			data.BankRegion,
			data.ExpectedPaymentDate.Format("2006-01-02 15:04:05"),
			data.Remarks,
			len(data.Attachments),
			data.SourceInstanceCode,
			data.ApprovalCode,
			data.TransformTime.Format("2006-01-02 15:04:05"),
			data.DataVersion,
		}

		for i, value := range values {
			cell := fmt.Sprintf("%s%d", string(rune('A'+i)), row)
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// 如果有附件，创建附件详情工作表
	if len(data.Attachments) > 0 {
		attachmentSheetName := "附件详情"
		_, err := f.NewSheet(attachmentSheetName)
		if err != nil {
			return fmt.Errorf("创建附件工作表失败: %w", err)
		}

		// 附件表头
		attachmentHeaders := []string{"序号", "文件名", "文件URL"}
		for i, header := range attachmentHeaders {
			cell := fmt.Sprintf("%s1", string(rune('A'+i)))
			f.SetCellValue(attachmentSheetName, cell, header)
		}

		// 应用表头样式
		f.SetCellStyle(attachmentSheetName, "A1", "C1", headerStyle)

		// 写入附件数据
		for i, attachment := range data.Attachments {
			rowNum := i + 2
			f.SetCellValue(attachmentSheetName, fmt.Sprintf("A%d", rowNum), i+1)
			f.SetCellValue(attachmentSheetName, fmt.Sprintf("B%d", rowNum), attachment.FileName)
			f.SetCellValue(attachmentSheetName, fmt.Sprintf("C%d", rowNum), attachment.FileURL)
		}

		// 自动调整列宽
		f.SetColWidth(attachmentSheetName, "A", "A", 8)
		f.SetColWidth(attachmentSheetName, "B", "B", 50)
		f.SetColWidth(attachmentSheetName, "C", "C", 80)
	}

	// 设置活动工作表
	f.SetActiveSheet(index)

	// 自动调整主表列宽
	for i := 0; i < len(headers); i++ {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 20)
	}

	// 保存文件
	if err := f.SaveAs(filename); err != nil {
		return fmt.Errorf("保存Excel文件失败: %w", err)
	}

	// 获取当前工作目录以显示完整路径
	if wd, err := os.Getwd(); err == nil {
		fullPath := filepath.Join(wd, filename)
		fmt.Printf("Excel文件已保存到: %s\n", fullPath)
	} else {
		fmt.Printf("Excel文件已保存: %s\n", filename)
	}
	return nil
}

// formatTime 格式化时间，处理零值时间
func (tester *ContractTransformerTester) formatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// formatDate 格式化日期，处理零值时间
func (tester *ContractTransformerTester) formatDate(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format("2006-01-02")
}
