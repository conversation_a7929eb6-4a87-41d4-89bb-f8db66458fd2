# github.com/flipped-aurora/gin-vue-admin/server Global Configuration

ins-buy:
  # 指定部署的分店，默认是总店
  store-code:
  ext-pay:
    db:
      dialect: mysql
      dsn: "ins-dev:HHhMyekkwTrdD3mS@tcp(cd-ins.ultrasdk.com:8904)/ins-pay?charset=utf8mb4&parseTime=True&loc=Asia%2FShanghai"
      prefix: ""
      singular: true
kafka:
  topicmap:
    bookdesk: bookdesk-test
  groupid: bookdesk-test-cd-dev-group
  srv:
    - "alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093"
    - "alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093"
    - "alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"
  securityprotocol: sasl_ssl
  cafile: "./kafka-ali-only-4096-ca-cert.pem"
  sasl:
    mechanism: "PLAIN"
    username: "alikafka_pre-cn-5yd3iirzq001"
    password: "rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"
grpc:
  addr: ":8889"
  log-level: info


# jwt configuration
jwt:
  signing-key: 08f7aa81-5bdb-47bc-9c00-20e0021f0d65
  expires-time: 7d
  buffer-time: 1d
  issuer: qmPlus
# zap logger configuration
zap:
  level: info
  format: console
  prefix: "[insbuy]"
  director: log
  show-line: true
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  log-in-console: true
  max-age: 0

# redis configuration
redis:
  db: 10
  addr: 127.0.0.1:6379
  username: "root"
  password: ""

# email configuration
email:
  to: <EMAIL>
  port: 465
  from: <EMAIL>
  host: smtp.163.com
  is-ssl: true
  secret: xxx
  nickname: test

# system configuration
system:
  #env: public # Change to "develop" to skip authentication for development mode
  env: develop
  addr: 8888
  db-type: mysql
  oss-type: local # 控制oss选择走本地还是 七牛等其他仓 自行增加其他oss仓可以在 server/utils/upload/upload.go 中 NewOss函数配置
  use-redis: true # 使用redis
  use-multipoint: false
  # IP限制次数 一个小时15000次
  iplimit-count: 15000
  #  IP限制一个小时
  iplimit-time: 3600
  #  路由全局前缀
  router-prefix: "/insbuy-api"
  domain: http://127.0.0.1:8080/insbuy-api/
  db-auto-migrate-disabled: false # 禁用自动迁移
  db-orm-debug-enabled: true # 对orm的操作启用日志

# captcha configuration
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0 # 0代表一直开启，大于0代表限制次数
  open-captcha-timeout: 3600 # open-captcha大于0时才生效
  super-captcha: [ jar0,insVip ]

# mysql connect configuration
# 未初始化之前请勿手动修改数据库信息！！！如果一定要手动初始化请看（https://gin-vue-admin.com/docs/first_master）
mysql:
  path: cd-ins.ultrasdk.com
  port: "8904"
  config: charset=utf8mb4&parseTime=True&loc=Asia%2fShanghai
  db-name: ins-dev
  username: ins-dev
  password: HHhMyekkwTrdD3mS
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: "info"
  log-zap: false
    #  slave:
  #    - path: 127.0.0.1
  # port: "3306"
  # config: charset=utf8mb4&parseTime=True&loc=Asia%2fShanghai
  # db-name: ins-dev
  # username: root
  # password: root
  # max-idle-conns: 10
  # max-open-conns: 100

# pgsql connect configuration
# 未初始化之前请勿手动修改数据库信息！！！如果一定要手动初始化请看（https://gin-vue-admin.com/docs/first_master）
pgsql:
  path: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false
oracle:
  path: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false
mssql:
  path: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false
sqlite:
  path: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false
db-list:
  - disable: true # 是否禁用
    type: "" # 数据库的类型,目前支持mysql、pgsql、mssql、oracle
    alias-name: "" # 数据库的名称,注意: alias-name 需要在db-list中唯一
    path: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    max-idle-conns: 10
    max-open-conns: 100
    log-mode: ""
    log-zap: false

# local configuration
local:
  path: uploads/file
  store-path: uploads/file

# autocode configuration
autocode:
  transfer-restart: true
  # root 自动适配项目根目录
  # 请不要手动配置,他会在项目加载的时候识别出根路径
  root: ""
  server: /server
  server-plug: /plugin/%s
  server-api: /api/v1/%s
  server-initialize: /initialize
  server-model: /model/%s
  server-request: /model/%s/request/
  server-router: /router/%s
  server-service: /service/%s
  web: /web/src
  web-api: /api
  web-form: /view
  web-table: /view

# qiniu configuration (请自行七牛申请对应的 公钥 私钥 bucket 和 域名地址)
qiniu:
  zone: ZoneHuaDong
  bucket: ""
  img-path: ""
  use-https: false
  access-key: ""
  secret-key: ""
  use-cdn-domains: false

# aliyun oss configuration
aliyun-oss:
  endpoint: yourEndpoint
  access-key-id: yourAccessKeyId
  access-key-secret: yourAccessKeySecret
  bucket-name: yourBucketName
  bucket-url: yourBucketUrl
  base-path: yourBasePath

# tencent cos configuration
tencent-cos:
  bucket: xxxxx-10005608
  region: ap-shanghai
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# aws s3 configuration (minio compatible)
aws-s3:
  bucket: xxxxx-10005608
  region: ap-shanghai
  endpoint: ""
  s3-force-path-style: false
  disable-ssl: false
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# huawei obs configuration
hua-wei-obs:
  path: you-path
  bucket: you-bucket
  endpoint: you-endpoint
  access-key: you-access-key
  secret-key: you-secret-key

# excel configuration
excel:
  dir: ./resource/excel/

resource:
  sqlTemplates: ./resource/templates/

# timer task db clear table
Timer:
  start: true
  spec: "@daily" # 定时任务详细配置参考 https://pkg.go.dev/github.com/robfig/cron/v3
  detail:
    - tableName: sys_operation_records
      compareField: created_at
      interval: 2160h
    - tableName: jwt_blacklists
      compareField: created_at
      interval: 168h
  insTask:
    #  - taskName: "MaterialInventoryTime"
    #    spec: "@every 5m"
    #    withSeconds: false
    #  - taskName: "AutoReviseOrderAmount"
    #     spec: "@every 1m"
  #    withSeconds: false
      #   - taskName: "RetryPrintTask"
      #     spec: "@every 1m"
    #     withSeconds: false
    #  - taskName: "SyncGateFlowSummary"
    #    spec: "@every 5m"
    #    withSeconds: false
    #  - taskName: "MaterialInventory"
    #     spec: "46 11 * * *"
    #      withSeconds : false
    #   - taskName: "MaterialInventoryTime"
    #       spec: "@every 5m"
    #       withSeconds : false
    #     - taskName: "DepositReport"
    #       spec: "@every 1m"
    #       withSeconds: false
    #     - taskName: "DepositBottomReport"
    #       spec: "0 2 * * *"
    #      #spec: "@every 5m"
    #      withSeconds: false
    # - taskName: "SyncOrderFromKezee"
    #   spec: "@every 1m"
    # - taskName: "AutoInventoryCheck"
    #   spec: "0 9 * * *"  # 每天早上9点执行自动盘点
    #   withSeconds: false

# 跨域配置
# 需要配合 server/initialize/router.go -> `Router.Use(middleware.CorsByRules())` 使用
cors:
  mode: strict-whitelist # 放行模式: allow-all, 放行全部; whitelist, 白名单模式, 来自白名单内域名的请求添加 cors 头; strict-whitelist 严格白名单模式, 白名单外的请求一律拒绝
  whitelist:
    - allow-origin: example1.com
      allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
      allow-methods: POST, GET
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true # 布尔值
    - allow-origin: example2.com
      allow-headers: content-type
      allow-methods: GET, POST
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true # 布尔值

## 阿里云翻译
aliyun-translation:
  access-key-id: LTAI5tFTKvLshrE3L6zSEXr3
  access-key-secret: ******************************

# 私人仓库配置
pri-inventory:
  apply-user-id: ["1075", "1132", "4", "1354"]

feishu-app:
  app-id: cli_a8084e417e9f101c
  app-secret: Xgl2hN6B37XxPFpQXuEFkdWpWkKd8pIY
  # 审批代码配置列表
  approval-codes:
    - code: "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
      name: "付款申请"
      description: "付款申请"
      enabled: true
      tags: []
    - code: "0B92F2B5-922F-4570-8A83-489E476FF811"
      name: "费用报销"
      description: "费用报销"
      enabled: true
      tags: [ ]
    - code: "A5D16C16-42DA-4080-BBE4-E53219EDDEC8"
      name: "付款申请（劳务）"
      description: "付款申请（劳务）"
      enabled: true
      tags: [ ]
    - code: "FAFE52D3-6F77-4676-B84A-507C90655149"
      name: "付款申请(薪酬)3.3版"
      description: "付款申请(薪酬)3.3版"
      enabled: true
      tags: [ ]
    - code: "A0A75C81-7481-42CB-B180-F42BB1B8AAAE"
      name: "INS合同审批流程"
      description: "INS合同审批流程"
      enabled: true
      tags: [ ]