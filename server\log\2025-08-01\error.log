[insbuy]2025/08/01 - 10:11:20.776	[31<PERSON><PERSON>r[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 10:23:01.769	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:32:04.861	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:35:16.217	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:35:16.509	[31merror[0m	test/contract_transformer_test.go:574	未找到匹配的映射规则	{"traceId": "58ac31309ac0bf147e20d0b4a0b97468", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "available_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[31merror[0m	test/contract_transformer_test.go:442	选择映射规则失败	{"traceId": "0592e5966fe7c08071f7b434677b85b9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "未找到审批代码 0B92F2B5-922F-4570-8A83-489E476FF811 的映射规则，可用规则: [F523F053-7AC6-4280-A4E7-B35E0C0431B5 PAYMENT_REQUEST_APPROVAL]"}
