[insbuy]2025/08/01 - 10:11:20.852	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 10:23:01.856	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 15:35:16.303	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 15:35:16.490	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "0592e5966fe7c08071f7b434677b85b9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 15:35:16.505	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "0592e5966fe7c08071f7b434677b85b9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 15:35:16.505	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 15:35:16.505	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "c784671b1c41114e2a0772f5879f8a7d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 15:35:16.506	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "2918877334b6bcfeaab7e401c66231a1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 15:35:16.507	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "2918877334b6bcfeaab7e401c66231a1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 15:35:16.507	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "2918877334b6bcfeaab7e401c66231a1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 15:35:16.507	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ed76d5044853b272051b7156767a2386", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ed76d5044853b272051b7156767a2386", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ed76d5044853b272051b7156767a2386", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "c784671b1c41114e2a0772f5879f8a7d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "58ac31309ac0bf147e20d0b4a0b97468", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
