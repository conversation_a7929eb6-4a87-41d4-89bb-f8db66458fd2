[insbuy]2025/08/05 - 10:43:56.257	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 10:43:56.430	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "2c132fe59c70710bda5cef0df38d6cbb", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 10:43:56.447	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "2c132fe59c70710bda5cef0df38d6cbb", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 10:43:56.447	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "88f972e3ef8f2415199da26901f4dac6", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 10:43:56.447	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "8f254c494cb5f15b6ce2b20d6fe9379d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 10:43:56.447	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "8221f1246cd02da1c4d6b1f337d08d4a", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 10:43:56.448	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "8221f1246cd02da1c4d6b1f337d08d4a", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 10:43:56.448	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "8221f1246cd02da1c4d6b1f337d08d4a", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 10:43:56.448	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4acb1ac1a42958e5403896c9a390739e", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 10:43:56.450	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4acb1ac1a42958e5403896c9a390739e", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 10:43:56.450	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4acb1ac1a42958e5403896c9a390739e", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 10:43:56.450	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "96ec11f2bec83bee3e6db9942a5b2de5", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "96ec11f2bec83bee3e6db9942a5b2de5", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "96ec11f2bec83bee3e6db9942a5b2de5", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "8f254c494cb5f15b6ce2b20d6fe9379d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "88f972e3ef8f2415199da26901f4dac6", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "88f972e3ef8f2415199da26901f4dac6", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "88f972e3ef8f2415199da26901f4dac6", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "88f972e3ef8f2415199da26901f4dac6", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "534bd3bff001b7612f3d2e8d70cf5d82", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "534bd3bff001b7612f3d2e8d70cf5d82", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "2c132fe59c70710bda5cef0df38d6cbb", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 10:43:56.451	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 10:43:56.452	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0006166}
[insbuy]2025/08/05 - 10:43:56.489	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 10:43:56.489	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0006166, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 10:43:56.452", "data_version": "1.0"}
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 10:43:56.490	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "2c132fe59c70710bda5cef0df38d6cbb", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_104356.xlsx", "success": false, "processing_time": 0.0006166}
[insbuy]2025/08/05 - 10:45:26.776	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 10:45:26.956	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "dfc3178a7a3432b360068355700ec203", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 10:45:26.970	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "dfc3178a7a3432b360068355700ec203", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 10:45:26.971	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "47caf6b7aaeba243365ea9abdce046d8", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 10:45:26.971	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "43b9ee696adeda892dd345288e9a9566", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 10:45:26.971	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "7f9ff2be5344de538e0150a6c840f0ac", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 10:45:26.973	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "7f9ff2be5344de538e0150a6c840f0ac", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 10:45:26.973	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "7f9ff2be5344de538e0150a6c840f0ac", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 10:45:26.973	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4fe65facfcbf9bf5b9c11eb29fb6f7fe", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 10:45:26.973	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4fe65facfcbf9bf5b9c11eb29fb6f7fe", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4fe65facfcbf9bf5b9c11eb29fb6f7fe", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "d3c6416d5ab0dc593d3e994e8c1a16dc", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "d3c6416d5ab0dc593d3e994e8c1a16dc", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "d3c6416d5ab0dc593d3e994e8c1a16dc", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "43b9ee696adeda892dd345288e9a9566", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "47caf6b7aaeba243365ea9abdce046d8", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "47caf6b7aaeba243365ea9abdce046d8", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "47caf6b7aaeba243365ea9abdce046d8", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "47caf6b7aaeba243365ea9abdce046d8", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "296d7d741353a5b42b65faed3972fed4", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "296d7d741353a5b42b65faed3972fed4", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 10:45:26.974	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "dfc3178a7a3432b360068355700ec203", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 10:45:26.975	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 10:45:26.975	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/08/05 - 10:45:27.010	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 10:45:26.975", "data_version": "1.0"}
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 10:45:27.011	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "dfc3178a7a3432b360068355700ec203", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_104527.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/08/05 - 11:07:24.598	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 11:07:24.790	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "526af0a532dc68dd8a401327bc3e5e6e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 11:07:24.805	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "526af0a532dc68dd8a401327bc3e5e6e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 11:07:24.805	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "7cdb2132e88726ef337301e1aafca026", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 11:07:24.805	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "ee3b120800c454386a16298e472212d3", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 11:07:24.806	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c542f6cfd5d1aad195afd072e227419f", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 11:07:24.809	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "c542f6cfd5d1aad195afd072e227419f", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:07:24.809	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "c542f6cfd5d1aad195afd072e227419f", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 11:07:24.809	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "cd34b5b4e2dbdb6ddb4d4c05df3db5f2", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 11:07:24.811	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "cd34b5b4e2dbdb6ddb4d4c05df3db5f2", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 11:07:24.811	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "cd34b5b4e2dbdb6ddb4d4c05df3db5f2", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 11:07:24.811	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4b06f34b7b7efebc26326f6093585303", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 11:07:24.812	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4b06f34b7b7efebc26326f6093585303", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4b06f34b7b7efebc26326f6093585303", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "ee3b120800c454386a16298e472212d3", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "7cdb2132e88726ef337301e1aafca026", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "7cdb2132e88726ef337301e1aafca026", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "7cdb2132e88726ef337301e1aafca026", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "7cdb2132e88726ef337301e1aafca026", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "ac0e032525c2c3ae2770a97578fe096f", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "ac0e032525c2c3ae2770a97578fe096f", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "526af0a532dc68dd8a401327bc3e5e6e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:07:24.813	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 11:07:24.813", "data_version": "1.0"}
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 11:07:24.848	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "526af0a532dc68dd8a401327bc3e5e6e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_110724.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/08/05 - 11:27:23.294	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 11:27:24.362	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "6454c265640dd47b8fcf853fff8f9fa9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 11:27:24.549	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "6454c265640dd47b8fcf853fff8f9fa9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 11:27:24.549	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "b9dfef8d56a9c5048d40c804fbbd1225", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 11:27:24.549	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "cf94181cbf306cbdb58bc096f43be998", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 11:27:24.549	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c2f23efaca14bdd3fa9a630506f81142", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 11:27:24.553	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "c2f23efaca14bdd3fa9a630506f81142", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:27:24.553	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "c2f23efaca14bdd3fa9a630506f81142", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 11:27:24.553	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "49b98d8c9998ff927431dca82b8968c7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 11:27:24.555	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "49b98d8c9998ff927431dca82b8968c7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 11:27:24.555	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "49b98d8c9998ff927431dca82b8968c7", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 11:27:24.555	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "cb49850867323c8f4565c82f3894d7cc", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "cb49850867323c8f4565c82f3894d7cc", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "cb49850867323c8f4565c82f3894d7cc", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "cf94181cbf306cbdb58bc096f43be998", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "b9dfef8d56a9c5048d40c804fbbd1225", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "b9dfef8d56a9c5048d40c804fbbd1225", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "b9dfef8d56a9c5048d40c804fbbd1225", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "b9dfef8d56a9c5048d40c804fbbd1225", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "eeab4be7a3886427f98a5e4ce5d92786", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "eeab4be7a3886427f98a5e4ce5d92786", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 11:27:24.556	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "6454c265640dd47b8fcf853fff8f9fa9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:27:24.557	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:27:24.558	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005274}
[insbuy]2025/08/05 - 11:27:24.592	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 11:27:24.592	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 11:27:24.592	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005274, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 11:27:24.558", "data_version": "1.0"}
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 11:27:24.593	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "6454c265640dd47b8fcf853fff8f9fa9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_112724.xlsx", "success": false, "processing_time": 0.0005274}
[insbuy]2025/08/05 - 11:32:00.982	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 11:32:01.144	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "30d4c2579884317957abb1c543e903a3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 11:32:01.160	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "30d4c2579884317957abb1c543e903a3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 11:32:01.160	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "57c56aa4d15847ef71cf5df796615b53", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 11:32:01.160	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "5d3c15a32044686a356367038fda740e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 11:32:01.161	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "a7c680315323e33ba23e1ba9273a77a4", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 11:32:01.162	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "a7c680315323e33ba23e1ba9273a77a4", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:32:01.162	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "a7c680315323e33ba23e1ba9273a77a4", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 11:32:01.162	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b8c08e95ae71ec16a2d88746c312d524", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 11:32:01.163	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "b8c08e95ae71ec16a2d88746c312d524", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 11:32:01.163	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "b8c08e95ae71ec16a2d88746c312d524", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 11:32:01.163	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "0ae30e89004ab7b5bcf9106c1ed9c7e7", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "0ae30e89004ab7b5bcf9106c1ed9c7e7", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "0ae30e89004ab7b5bcf9106c1ed9c7e7", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "5d3c15a32044686a356367038fda740e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "57c56aa4d15847ef71cf5df796615b53", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "57c56aa4d15847ef71cf5df796615b53", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "57c56aa4d15847ef71cf5df796615b53", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "57c56aa4d15847ef71cf5df796615b53", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "82cc15be2e6f39f62140e39c4d8a7eb9", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "82cc15be2e6f39f62140e39c4d8a7eb9", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 11:32:01.164	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "30d4c2579884317957abb1c543e903a3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:32:01.165	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:32:01.165	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 11:32:01.165", "data_version": "1.0"}
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 11:32:01.203	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "30d4c2579884317957abb1c543e903a3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_113201.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/08/05 - 11:33:19.775	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 11:33:19.918	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "ec4cec399fadce8f747e50b8be0a789e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 11:33:20.001	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "ec4cec399fadce8f747e50b8be0a789e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 11:33:20.001	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "6bd91981217e6f6d1b5e4e1f172f18a5", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 11:33:20.001	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "1d09434399fe05d626b6abb437c6fab2", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 11:33:20.001	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c13ce461ca16e2901fdb4690b765dd2c", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 11:33:20.004	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "c13ce461ca16e2901fdb4690b765dd2c", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:33:20.004	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "c13ce461ca16e2901fdb4690b765dd2c", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 11:33:20.004	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "41294e3339091cd1b1aca272fb6fdeeb", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 11:33:20.006	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "41294e3339091cd1b1aca272fb6fdeeb", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 11:33:20.006	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "41294e3339091cd1b1aca272fb6fdeeb", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 11:33:20.007	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "1778bbec41506d9855f389115b4d85cd", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "1778bbec41506d9855f389115b4d85cd", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "1778bbec41506d9855f389115b4d85cd", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "1d09434399fe05d626b6abb437c6fab2", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "6bd91981217e6f6d1b5e4e1f172f18a5", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "6bd91981217e6f6d1b5e4e1f172f18a5", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "6bd91981217e6f6d1b5e4e1f172f18a5", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "6bd91981217e6f6d1b5e4e1f172f18a5", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 11:33:20.008	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "87b8e90a54edbfcc61759f3d159564e4", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:33:20.009	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "87b8e90a54edbfcc61759f3d159564e4", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 11:33:20.009	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "ec4cec399fadce8f747e50b8be0a789e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:33:20.009	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:33:20.009	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 11:33:20.009", "data_version": "1.0"}
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 11:33:20.051	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "ec4cec399fadce8f747e50b8be0a789e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_113320.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/08/05 - 11:35:14.736	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 11:35:14.899	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "47b90d52eb68c0776f1e665f19295334", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 11:35:14.914	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "47b90d52eb68c0776f1e665f19295334", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 11:35:14.915	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "de337db5facaaaf63ee1949a4f38b82e", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 11:35:14.915	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "50aba57566363cb61511fa61a517222e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 11:35:14.915	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "0fe301d9b1854998f3b8e6b66c73988e", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 11:35:14.918	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "0fe301d9b1854998f3b8e6b66c73988e", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:35:14.918	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "0fe301d9b1854998f3b8e6b66c73988e", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 11:35:14.918	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "fe2f35992e07103282168b9c4a1b12af", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 11:35:14.920	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "fe2f35992e07103282168b9c4a1b12af", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 11:35:14.920	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "fe2f35992e07103282168b9c4a1b12af", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 11:35:14.920	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "24fe935ae15c543d687a9c804adee9c6", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "24fe935ae15c543d687a9c804adee9c6", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "24fe935ae15c543d687a9c804adee9c6", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "50aba57566363cb61511fa61a517222e", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "de337db5facaaaf63ee1949a4f38b82e", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "de337db5facaaaf63ee1949a4f38b82e", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "de337db5facaaaf63ee1949a4f38b82e", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "de337db5facaaaf63ee1949a4f38b82e", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "2e682586e6a0a5a5b3952e20b4127caf", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "2e682586e6a0a5a5b3952e20b4127caf", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 11:35:14.922	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "47b90d52eb68c0776f1e665f19295334", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:35:14.923	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:35:14.923	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 11:35:14.959	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 11:35:14.960	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 11:35:14.960	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 11:35:14.923", "data_version": "1.0"}
[insbuy]2025/08/05 - 11:35:14.960	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 11:35:14.960	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "47b90d52eb68c0776f1e665f19295334", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_113514.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/08/05 - 11:38:55.250	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 11:38:55.400	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "7fa64a0731b77a6eb66d9b03470c31d7", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 11:38:55.481	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "7fa64a0731b77a6eb66d9b03470c31d7", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 11:38:55.481	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "0f0c9b821e601637ecd8cae663644768", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 11:38:55.481	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "8a0363db1de573682fc35cb9df16e7ea", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 11:38:55.481	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "d8ca21aa3a19046309476f5217c30804", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 11:38:55.484	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "d8ca21aa3a19046309476f5217c30804", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:38:55.484	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "d8ca21aa3a19046309476f5217c30804", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 11:38:55.484	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "3a9a853b12dfd783b4e45ebb0e2c2872", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 11:38:55.486	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "3a9a853b12dfd783b4e45ebb0e2c2872", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 11:38:55.486	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "3a9a853b12dfd783b4e45ebb0e2c2872", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 11:38:55.486	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "7af05ebc1884acc075d770e61bfd9ea6", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "7af05ebc1884acc075d770e61bfd9ea6", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "7af05ebc1884acc075d770e61bfd9ea6", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "8a0363db1de573682fc35cb9df16e7ea", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "0f0c9b821e601637ecd8cae663644768", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "0f0c9b821e601637ecd8cae663644768", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "0f0c9b821e601637ecd8cae663644768", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "0f0c9b821e601637ecd8cae663644768", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "7b72ccbaae9078fa27e805ab3def6c72", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "7b72ccbaae9078fa27e805ab3def6c72", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 11:38:55.488	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "7fa64a0731b77a6eb66d9b03470c31d7", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:38:55.489	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:38:55.490	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005291}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005291, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 11:38:55.526	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 11:38:55.527	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 11:38:55.527	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 11:38:55.527	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 11:38:55.490", "data_version": "1.0"}
[insbuy]2025/08/05 - 11:38:55.527	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 11:38:55.527	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "7fa64a0731b77a6eb66d9b03470c31d7", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_113855.xlsx", "success": false, "processing_time": 0.0005291}
[insbuy]2025/08/05 - 11:46:38.635	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/05 - 11:46:38.866	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "393c8a87d0a9429148f2e24f1c3a3146", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/05 - 11:46:38.882	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "393c8a87d0a9429148f2e24f1c3a3146", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/05 - 11:46:38.882	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "307cca7a29d4880fbd88ed255fd8ef4c", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/05 - 11:46:38.882	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "e81dd76bcf532370769d65291f422a33", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/05 - 11:46:38.883	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b426b31c2a6142b25bc7245f4a63e6fb", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/05 - 11:46:38.895	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "b426b31c2a6142b25bc7245f4a63e6fb", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:46:38.895	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "b426b31c2a6142b25bc7245f4a63e6fb", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/05 - 11:46:38.896	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "2d6ee3a6042d94f0875a594b46545c10", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/05 - 11:46:38.897	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "2d6ee3a6042d94f0875a594b46545c10", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/05 - 11:46:38.897	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "2d6ee3a6042d94f0875a594b46545c10", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/05 - 11:46:38.898	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c2db4452bd492cd2fb18e25a1b06fc94", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "c2db4452bd492cd2fb18e25a1b06fc94", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "c2db4452bd492cd2fb18e25a1b06fc94", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "e81dd76bcf532370769d65291f422a33", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "307cca7a29d4880fbd88ed255fd8ef4c", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "307cca7a29d4880fbd88ed255fd8ef4c", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "307cca7a29d4880fbd88ed255fd8ef4c", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "307cca7a29d4880fbd88ed255fd8ef4c", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "d4b007825810f9f0b5e06ea08940ad23", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "d4b007825810f9f0b5e06ea08940ad23", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/05 - 11:46:38.901	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "393c8a87d0a9429148f2e24f1c3a3146", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/05 - 11:46:38.902	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/05 - 11:46:38.966	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0632679}
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0632679, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/05 - 11:46:38.975	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/05 - 11:46:38.976	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/05 - 11:46:38.976	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/05 - 11:46:38.976	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/05 - 11:46:38.976	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/05 - 11:46:38.976	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/05 - 11:46:38.966", "data_version": "1.0"}
[insbuy]2025/08/05 - 11:46:38.976	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/05 - 11:46:38.976	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "393c8a87d0a9429148f2e24f1c3a3146", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250805_114638.xlsx", "success": false, "processing_time": 0.0632679}
