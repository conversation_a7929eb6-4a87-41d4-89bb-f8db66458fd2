[insbuy]2025/08/05 - 10:43:56.452	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "2c132fe59c70710bda5cef0df38d6cbb", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 10:45:26.975	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "dfc3178a7a3432b360068355700ec203", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:07:24.813	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "526af0a532dc68dd8a401327bc3e5e6e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:27:24.558	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "6454c265640dd47b8fcf853fff8f9fa9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:32:01.165	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "30d4c2579884317957abb1c543e903a3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:33:20.009	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "ec4cec399fadce8f747e50b8be0a789e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:35:14.923	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "47b90d52eb68c0776f1e665f19295334", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:38:55.490	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "7fa64a0731b77a6eb66d9b03470c31d7", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
