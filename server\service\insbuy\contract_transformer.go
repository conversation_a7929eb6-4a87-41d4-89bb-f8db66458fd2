package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insfinance"
	"go.uber.org/zap"
)

// AttachmentInfo 附件信息结构体
type AttachmentInfo struct {
	FileName string `json:"file_name"` // 文件名
	FileURL  string `json:"file_url"`  // 文件URL
}

// ExpenseDetailItem 费用明细项
type ExpenseDetailItem struct {
	ExpenseType    string    `json:"expense_type"`     // 费用类型
	Location       string    `json:"location"`         // 地点
	DateRange      string    `json:"date_range"`       // 日期区间
	StartDate      time.Time `json:"start_date"`       // 开始日期
	EndDate        time.Time `json:"end_date"`         // 结束日期
	VatInvoiceType string    `json:"vat_invoice_type"` // 增值税发票类型
	Amount         float64   `json:"amount"`           // 金额
	AmountCurrency string    `json:"amount_currency"`  // 金额币种
	AmountCapital  string    `json:"amount_capital"`   // 金额大写
	RowIndex       int       `json:"row_index"`        // 行索引（在原列表中的位置）
}

// StandardContractData 标准化合同数据结构
type StandardContractData struct {
	// 基础信息
	ApplicationNumber string    `json:"application_number"` // 申请编号
	Title             string    `json:"title"`              // 标题
	ApplicationStatus string    `json:"application_status"` // 申请状态
	InitiateTime      time.Time `json:"initiate_time"`      // 发起时间
	CompleteTime      time.Time `json:"complete_time"`      // 完成时间

	// 人员信息
	InitiatorEmployeeId   string `json:"initiator_employee_id"`   // 发起人工号
	InitiatorUserId       string `json:"initiator_user_id"`       // 发起人User ID
	InitiatorName         string `json:"initiator_name"`          // 发起人姓名
	InitiatorDepartment   string `json:"initiator_department"`    // 发起人部门
	InitiatorDepartmentId string `json:"initiator_department_id"` // 发起人部门ID
	DepartmentManager     string `json:"department_manager"`      // 部门负责人

	// 审批流程
	HistoryApprovers []string `json:"history_approvers"` // 历史审批人
	HistoryHandlers  []string `json:"history_handlers"`  // 历史办理人
	ApprovalRecords  []string `json:"approval_records"`  // 审批记录
	CurrentHandler   string   `json:"current_handler"`   // 当前处理人
	ApprovalNode     string   `json:"approval_node"`     // 审批节点
	ApproverCount    int      `json:"approver_count"`    // 审批人数
	ApprovalDuration int64    `json:"approval_duration"` // 审批耗时（分钟）
	SerialNumber     string   `json:"serial_number"`     // 流水号

	// 业务字段
	PaymentReason     string `json:"payment_reason"`      // 付款事由
	PaymentEntity     string `json:"payment_entity"`      // 付款主体
	BusinessType      string `json:"business_type"`       // 业务类型
	ProjectType       string `json:"project_type"`        // 工程类型
	MusicStore        string `json:"music_store"`         // 音乐单店
	PurchaseType      string `json:"purchase_type"`       // 采购类型
	PurchaseStoreName string `json:"purchase_store_name"` // 单店采购店名
	ExpenseDepartment string `json:"expense_department"`  // 费用所属部门

	// 金额信息
	PaymentCurrency      string  `json:"payment_currency"`       // 付款币种
	UsdAmount            float64 `json:"usd_amount"`             // 美元金额
	EurAmount            float64 `json:"eur_amount"`             // 欧元金额
	JpyAmount            float64 `json:"jpy_amount"`             // 日元金额
	HkdAmount            float64 `json:"hkd_amount"`             // 港币金额
	GbpAmount            float64 `json:"gbp_amount"`             // 英镑金额
	UsdToRmbAmount       float64 `json:"usd_to_rmb_amount"`      // 美元换算人民币
	EurToRmbAmount       float64 `json:"eur_to_rmb_amount"`      // 欧元换算人民币
	JpyToRmbAmount       float64 `json:"jpy_to_rmb_amount"`      // 日元换算人民币
	HkdToRmbAmount       float64 `json:"hkd_to_rmb_amount"`      // 港币换算人民币
	GbpToRmbAmount       float64 `json:"gbp_to_rmb_amount"`      // 英镑换算人民币
	ContractSignAmount   float64 `json:"contract_sign_amount"`   // 合同签约金额
	ContractPaidAmount   float64 `json:"contract_paid_amount"`   // 合同已付金额
	CurrentRequestAmount float64 `json:"current_request_amount"` // 本次请款金额

	// 财务信息
	VatInvoiceType       string  `json:"vat_invoice_type"`       // 增值税发票类型
	TaxRate              float64 `json:"tax_rate"`               // 税率
	AmountExcludingTax   float64 `json:"amount_excluding_tax"`   // 不含税金额
	ContractPaymentTerms string  `json:"contract_payment_terms"` // 合同付款条件

	// 银行信息
	AccountType     string `json:"account_type"`      // 账户类型
	BankName        string `json:"bank_name"`         // 银行
	AccountHolder   string `json:"account_holder"`    // 户名
	AccountNumber   string `json:"account_number"`    // 账号
	BankBranch      string `json:"bank_branch"`       // 银行支行
	BankRegion      string `json:"bank_region"`       // 银行所在地区
	SwiftCode       string `json:"swift_code"`        // Swift代码
	InterBankNumber string `json:"inter_bank_number"` // 联行号

	// 其他
	ExpectedPaymentDate time.Time        `json:"expected_payment_date"` // 期望付款日期
	Remarks             string           `json:"remarks"`               // 备注说明
	Attachments         []AttachmentInfo `json:"attachments"`           // 附件

	// 费用报销相关字段
	ReimbursementReason string              `json:"reimbursement_reason"` // 报销事由
	ReimbursementEntity string              `json:"reimbursement_entity"` // 报销主体
	TotalAmount         float64             `json:"total_amount"`         // 费用总金额
	ExpenseDetails      []ExpenseDetailItem `json:"expense_details"`      // 费用明细列表
	RecordType          string              `json:"record_type"`          // 记录类型（用于区分主记录和明细记录）

	// 元数据
	SourceInstanceCode string    `json:"source_instance_code"` // 源实例代码
	ApprovalCode       string    `json:"approval_code"`        // 审批代码
	TransformTime      time.Time `json:"transform_time"`       // 转换时间
	DataVersion        string    `json:"data_version"`         // 数据版本
}

// ContractTransformer 合同数据转换器
type ContractTransformer struct {
	mappingRules map[string]*FieldMappingRule
	validators   []DataValidator
	processors   []DataProcessor
}

func (t *ContractTransformer) SetMappingRules(mappingRules map[string]*FieldMappingRule) {
	t.mappingRules = mappingRules
}

func (t *ContractTransformer) MappingRules() map[string]*FieldMappingRule {
	return t.mappingRules
}

// FieldMappingRule 字段映射规则
type FieldMappingRule struct {
	ApprovalCode    string                 `json:"approval_code" yaml:"approval_code"`       // 审批代码
	ApprovalName    string                 `json:"approval_name" yaml:"approval_name"`       // 审批名称
	FieldMappings   map[string]FieldConfig `json:"field_mappings" yaml:"field_mappings"`     // 字段映射配置
	DefaultValues   map[string]interface{} `json:"default_values" yaml:"default_values"`     // 默认值
	RequiredFields  []string               `json:"required_fields" yaml:"required_fields"`   // 必填字段
	ValidationRules []ValidationRule       `json:"validation_rules" yaml:"validation_rules"` // 验证规则
}

// FieldConfig 字段配置
type FieldConfig struct {
	SourcePath      string                 `json:"source_path" yaml:"source_path"`             // 源字段路径（支持嵌套，如 "form.field_001.value"）
	TargetField     string                 `json:"target_field" yaml:"target_field"`           // 目标字段名
	DataType        string                 `json:"data_type" yaml:"data_type"`                 // 数据类型：string, int, float, bool, time, array, fieldList
	DefaultValue    interface{}            `json:"default_value" yaml:"default_value"`         // 默认值
	Transform       string                 `json:"transform" yaml:"transform"`                 // 转换函数名
	Required        bool                   `json:"required" yaml:"required"`                   // 是否必填
	Validation      string                 `json:"validation" yaml:"validation"`               // 验证规则
	Description     string                 `json:"description" yaml:"description"`             // 字段描述
	FieldListConfig map[string]FieldConfig `json:"field_list_config" yaml:"field_list_config"` // fieldList 类型的内部字段配置
}

// ValidationRule 验证规则
type ValidationRule struct {
	Field     string `json:"field" yaml:"field"`         // 字段名
	Rule      string `json:"rule" yaml:"rule"`           // 规则类型：required, format, range, custom
	Parameter string `json:"parameter" yaml:"parameter"` // 规则参数
	Message   string `json:"message" yaml:"message"`     // 错误消息
}

// DataValidator 数据验证器接口
type DataValidator interface {
	Validate(data *StandardContractData) []ValidationError
}

// DataProcessor 数据处理器接口
type DataProcessor interface {
	Process(data *StandardContractData) error
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value"`
}

// TransformResult 转换结果
type TransformResult struct {
	Success        bool                   `json:"success"`
	Data           *StandardContractData  `json:"data"`
	Errors         []ValidationError      `json:"errors"`
	Warnings       []string               `json:"warnings"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// NewContractTransformer 创建合同数据转换器
func NewContractTransformer() *ContractTransformer {
	transformer := &ContractTransformer{
		mappingRules: make(map[string]*FieldMappingRule),
		validators:   make([]DataValidator, 0),
		processors:   make([]DataProcessor, 0),
	}

	// 加载默认映射规则
	//transformer.loadDefaultMappingRules()

	// 注册默认验证器
	transformer.registerDefaultValidators()

	// 注册默认处理器
	transformer.registerDefaultProcessors()

	return transformer
}

// AddMappingRule 添加映射规则
func (t *ContractTransformer) AddMappingRule(approvalCode string, rule *FieldMappingRule) {
	t.mappingRules[approvalCode] = rule
}

// Transform 转换合同数据
func (t *ContractTransformer) Transform(ctx context.Context, contract *insfinance.ContractDetails) (*TransformResult, error) {
	startTime := time.Now()

	logger := global.GVA_LOG.With(
		zap.String("instance_code", contract.InstanceCode),
		zap.String("approval_code", contract.ApprovalCode),
	)

	logger.Info("开始转换合同数据")

	result := &TransformResult{
		Success:  true,
		Errors:   make([]ValidationError, 0),
		Warnings: make([]string, 0),
		Metadata: make(map[string]interface{}),
	}
	// 获取映射规则
	mappingRule, exists := t.mappingRules[contract.ApprovalCode]
	if !exists {
		// 尝试使用通用映射规则
		mappingRule = t.mappingRules["default"]
		if mappingRule == nil {
			return nil, fmt.Errorf("未找到审批代码 %s 的映射规则", contract.ApprovalCode)
		}
		result.Warnings = append(result.Warnings, fmt.Sprintf("使用默认映射规则处理审批代码: %s", contract.ApprovalCode))
	}
	// 执行数据转换
	standardData, err := t.executeTransform(contract, mappingRule)
	if err != nil {
		logger.Error("数据转换失败", zap.Error(err))
		result.Success = false
		result.Errors = append(result.Errors, ValidationError{
			Field:   "transform",
			Message: err.Error(),
			Value:   contract.InstanceCode,
		})
		return result, err
	}
	// 执行数据验证
	for _, validator := range t.validators {
		validationErrors := validator.Validate(standardData)
		result.Errors = append(result.Errors, validationErrors...)
	}

	// 执行数据处理
	for _, processor := range t.processors {
		if err := processor.Process(standardData); err != nil {
			logger.Error("数据处理失败", zap.Error(err))
			result.Warnings = append(result.Warnings, fmt.Sprintf("数据处理警告: %v", err))
		}
	}

	// 设置元数据
	standardData.SourceInstanceCode = contract.InstanceCode
	standardData.ApprovalCode = contract.ApprovalCode
	standardData.TransformTime = time.Now()
	standardData.DataVersion = "1.0"

	result.Data = standardData
	result.ProcessingTime = time.Since(startTime)
	result.Success = len(result.Errors) == 0

	// 记录转换结果
	logger.Info("合同数据转换完成",
		zap.Bool("success", result.Success),
		zap.Int("error_count", len(result.Errors)),
		zap.Int("warning_count", len(result.Warnings)),
		zap.Duration("processing_time", result.ProcessingTime),
	)

	return result, nil
}

// executeTransform 执行数据转换
func (t *ContractTransformer) executeTransform(contract *insfinance.ContractDetails, rule *FieldMappingRule) (*StandardContractData, error) {
	data := &StandardContractData{}

	// 解析表单数据
	formData, err := t.parseFormData(contract.Form)
	if err != nil {
		return nil, fmt.Errorf("解析表单数据失败: %w", err)
	}

	// 构建数据源映射
	sourceData := map[string]interface{}{
		"form":         formData,
		"timeline":     contract.Timeline,
		"comment_list": contract.CommentList,
		"task_list":    contract.TaskList,
		"basic": map[string]interface{}{
			"approval_code": contract.ApprovalCode,
			"approval_name": contract.ApprovalName,
			"instance_code": contract.InstanceCode,
			"department_id": contract.DepartmentId,
			"end_time":      contract.EndTime,
			"open_id":       contract.OpenId,
			"reverted":      contract.Reverted,
			"serial_number": contract.SerialNumber,
			"start_time":    contract.StartTime,
			"status":        contract.Status,
			"user_id":       contract.UserId,
			"uuid":          contract.Uuid,
		},
	}
	// 执行字段映射
	for targetField, fieldConfig := range rule.FieldMappings {
		value, err := t.extractFieldValue(sourceData, fieldConfig)
		if err != nil {
			if fieldConfig.Required {
				return nil, fmt.Errorf("提取必填字段 %s 失败: %w", targetField, err)
			}
			// 使用默认值
			value = fieldConfig.DefaultValue
		}

		// 应用转换函数
		if fieldConfig.Transform != "" {
			value = t.applyTransform(value, fieldConfig.Transform)
		}

		// 设置字段值
		if fieldConfig.DataType == "fieldList" {
			// 特殊处理 fieldList 类型
			if err := t.processFieldList(data, targetField, value, fieldConfig); err != nil {
				return nil, fmt.Errorf("处理 fieldList 字段 %s 失败: %w", targetField, err)
			}
		} else {
			// 普通字段处理
			if err := t.setFieldValue(data, targetField, value, fieldConfig.DataType); err != nil {
				return nil, fmt.Errorf("设置字段 %s 失败: %w", targetField, err)
			}
		}
	}

	// 应用默认值
	for field, defaultValue := range rule.DefaultValues {
		if t.isFieldEmpty(data, field) {
			t.setFieldValue(data, field, defaultValue, "string")
		}
	}

	return data, nil
}

// parseFormData 解析表单数据
func (t *ContractTransformer) parseFormData(formJson string) (map[string]interface{}, error) {
	if formJson == "" {
		return make(map[string]interface{}), nil
	}

	var formData map[string]interface{}
	if err := json.Unmarshal([]byte(formJson), &formData); err != nil {
		return nil, fmt.Errorf("解析表单JSON失败: %w", err)
	}

	return formData, nil
}

// extractFieldValue 提取字段值
func (t *ContractTransformer) extractFieldValue(sourceData map[string]interface{}, config FieldConfig) (interface{}, error) {
	// 解析路径
	pathParts := strings.Split(config.SourcePath, ".")

	current := sourceData
	for i, part := range pathParts {
		if current == nil {
			return nil, fmt.Errorf("路径 %s 在第 %d 部分为空", config.SourcePath, i)
		}

		// 处理数组索引
		if strings.Contains(part, "[") && strings.Contains(part, "]") {
			arrayName := part[:strings.Index(part, "[")]
			indexStr := part[strings.Index(part, "[")+1 : strings.Index(part, "]")]

			arrayValue, exists := current[arrayName]
			if !exists {
				return nil, fmt.Errorf("数组字段 %s 不存在", arrayName)
			}

			arraySlice, ok := arrayValue.([]interface{})
			if !ok {
				return nil, fmt.Errorf("字段 %s 不是数组类型", arrayName)
			}

			index, err := strconv.Atoi(indexStr)
			if err != nil {
				return nil, fmt.Errorf("无效的数组索引: %s", indexStr)
			}

			if index >= len(arraySlice) {
				return nil, fmt.Errorf("数组索引 %d 超出范围", index)
			}

			if i == len(pathParts)-1 {
				return arraySlice[index], nil
			}

			current, ok = arraySlice[index].(map[string]interface{})
			if !ok {
				return nil, fmt.Errorf("数组元素不是对象类型")
			}
		} else {
			// 普通字段访问
			if i == len(pathParts)-1 {
				value, exists := current[part]
				if !exists {
					return nil, fmt.Errorf("字段 %s 不存在", part)
				}
				return value, nil
			}

			nextValue, exists := current[part]
			if !exists {
				return nil, fmt.Errorf("字段 %s 不存在", part)
			}

			var ok bool
			current, ok = nextValue.(map[string]interface{})
			if !ok {
				return nil, fmt.Errorf("字段 %s 不是对象类型", part)
			}
		}
	}

	return nil, fmt.Errorf("无法提取字段值")
}

// setFieldValue 设置字段值
func (t *ContractTransformer) setFieldValue(data *StandardContractData, fieldName string, value interface{}, dataType string) error {
	if value == nil {
		return nil
	}

	// 根据字段名设置对应的字段值
	switch fieldName {
	case "application_number":
		data.ApplicationNumber = t.convertToString(value)
	case "title":
		data.Title = t.convertToString(value)
	case "application_status":
		data.ApplicationStatus = t.convertToString(value)
	case "initiate_time":
		if timeValue, err := t.convertToTime(value); err == nil {
			data.InitiateTime = timeValue
		}
	case "complete_time":
		if timeValue, err := t.convertToTime(value); err == nil {
			data.CompleteTime = timeValue
		}
	case "initiator_employee_id":
		data.InitiatorEmployeeId = t.convertToString(value)
	case "initiator_user_id":
		data.InitiatorUserId = t.convertToString(value)
	case "initiator_name":
		data.InitiatorName = t.convertToString(value)
	case "initiator_department":
		data.InitiatorDepartment = t.convertToString(value)
	case "initiator_department_id":
		data.InitiatorDepartmentId = t.convertToString(value)
	case "department_manager":
		data.DepartmentManager = t.convertToString(value)
	case "serial_number":
		data.SerialNumber = t.convertToString(value)
	case "payment_reason":
		data.PaymentReason = t.convertToString(value)
	case "payment_entity", "payment_company":
		data.PaymentEntity = t.convertToString(value)
	case "business_type":
		data.BusinessType = t.convertToString(value)
	case "project_type":
		data.ProjectType = t.convertToString(value)
	case "music_store":
		data.MusicStore = t.convertToString(value)
	case "purchase_type":
		data.PurchaseType = t.convertToString(value)
	case "purchase_store_name":
		data.PurchaseStoreName = t.convertToString(value)
	case "expense_department":
		data.ExpenseDepartment = t.convertToString(value)
	case "payment_currency":
		data.PaymentCurrency = t.convertToString(value)
	case "usd_amount":
		data.UsdAmount = t.convertToFloat64(value)
	case "eur_amount":
		data.EurAmount = t.convertToFloat64(value)
	case "jpy_amount":
		data.JpyAmount = t.convertToFloat64(value)
	case "hkd_amount":
		data.HkdAmount = t.convertToFloat64(value)
	case "gbp_amount":
		data.GbpAmount = t.convertToFloat64(value)
	case "contract_sign_amount":
		data.ContractSignAmount = t.convertToFloat64(value)
	case "contract_paid_amount":
		data.ContractPaidAmount = t.convertToFloat64(value)
	case "current_request_amount":
		data.CurrentRequestAmount = t.convertToFloat64(value)

	case "vat_invoice_type":
		data.VatInvoiceType = t.convertToString(value)
	case "tax_rate":
		data.TaxRate = t.convertToFloat64(value)
	case "amount_excluding_tax", "tax_excluded_amount":
		data.AmountExcludingTax = t.convertToFloat64(value)
	case "contract_payment_terms":
		data.ContractPaymentTerms = t.convertToString(value)
	case "account_type":
		data.AccountType = t.convertToString(value)
	case "bank_name":
		data.BankName = t.convertToString(value)
	case "account_holder":
		data.AccountHolder = t.convertToString(value)
	case "account_number":
		data.AccountNumber = t.convertToString(value)
	case "bank_branch":
		data.BankBranch = t.convertToString(value)
	case "bank_region":
		data.BankRegion = t.convertToString(value)
	case "swift_code":
		data.SwiftCode = t.convertToString(value)
	case "inter_bank_number":
		data.InterBankNumber = t.convertToString(value)
	case "expected_payment_date":
		if timeValue, err := t.convertToTime(value); err == nil {
			data.ExpectedPaymentDate = timeValue
		}
	case "remarks":
		data.Remarks = t.convertToString(value)
	case "attachments":
		data.Attachments = t.convertToAttachmentInfoArray(value)

	// 费用报销相关字段
	case "reimbursement_reason":
		data.ReimbursementReason = t.convertToString(value)
	case "reimbursement_entity":
		data.ReimbursementEntity = t.convertToString(value)
	case "total_amount":
		data.TotalAmount = t.convertToFloat64(value)
	case "expense_details":
		// fieldList 类型字段的特殊处理
		if dataType == "fieldList" {
			data.ExpenseDetails = t.convertToExpenseDetailArray(value)
		}
	case "record_type":
		data.RecordType = t.convertToString(value)

	default:
		return fmt.Errorf("未知字段: %s", fieldName)
	}

	return nil
}

// isFieldEmpty 检查字段是否为空
func (t *ContractTransformer) isFieldEmpty(data *StandardContractData, fieldName string) bool {
	switch fieldName {
	case "application_number":
		return data.ApplicationNumber == ""
	case "title":
		return data.Title == ""
	case "application_status":
		return data.ApplicationStatus == ""
	case "initiator_employee_id":
		return data.InitiatorEmployeeId == ""
	case "initiator_user_id":
		return data.InitiatorUserId == ""
	case "initiator_name":
		return data.InitiatorName == ""
	case "payment_reason":
		return data.PaymentReason == ""
	case "payment_entity":
		return data.PaymentEntity == ""
	case "attachments":
		return len(data.Attachments) == 0
	// 可以根据需要添加更多字段检查
	default:
		return false
	}
}

// 数据类型转换方法

// convertToString 转换为字符串
func (t *ContractTransformer) convertToString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int8, int16, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%.2f", v)
	case bool:
		if v {
			return "true"
		}
		return "false"
	case []interface{}:
		// 处理数组，取第一个元素或转换为JSON字符串
		if len(v) > 0 {
			return t.convertToString(v[0])
		}
		return ""
	case map[string]interface{}:
		// 处理对象，尝试获取value字段
		if val, exists := v["value"]; exists {
			return t.convertToString(val)
		}
		if val, exists := v["text"]; exists {
			return t.convertToString(val)
		}
		// 转换为JSON字符串
		if jsonBytes, err := json.Marshal(v); err == nil {
			return string(jsonBytes)
		}
		return ""
	default:
		return fmt.Sprintf("%v", v)
	}
}

// convertToFloat64 转换为浮点数
func (t *ContractTransformer) convertToFloat64(value interface{}) float64 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int:
		return float64(v)
	case int8:
		return float64(v)
	case int16:
		return float64(v)
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case uint:
		return float64(v)
	case uint8:
		return float64(v)
	case uint16:
		return float64(v)
	case uint32:
		return float64(v)
	case uint64:
		return float64(v)
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f
		}
		return 0
	case map[string]interface{}:
		// 处理对象，尝试获取value字段
		if val, exists := v["value"]; exists {
			return t.convertToFloat64(val)
		}
		return 0
	default:
		return 0
	}
}

// convertToTime 转换为时间
func (t *ContractTransformer) convertToTime(value interface{}) (time.Time, error) {
	if value == nil {
		return time.Time{}, fmt.Errorf("时间值为空")
	}

	switch v := value.(type) {
	case time.Time:
		return v, nil
	case string:
		// 尝试多种时间格式
		formats := []string{
			"2006-01-02T15:04:05Z",
			"2006-01-02T15:04:05.000Z",
			"2006-01-02T15:04:05+08:00",
			"2006-01-02 15:04:05",
			"2006-01-02",
			"1136239445", // Unix timestamp as string
		}

		for _, format := range formats {
			if t, err := time.Parse(format, v); err == nil {
				return t, nil
			}
		}

		// 尝试解析Unix时间戳
		if timestamp, err := strconv.ParseInt(v, 10, 64); err == nil {
			// 判断是秒还是毫秒
			if timestamp > 1e10 {
				return time.Unix(timestamp/1000, (timestamp%1000)*1e6), nil
			}
			return time.Unix(timestamp, 0), nil
		}

		return time.Time{}, fmt.Errorf("无法解析时间字符串: %s", v)
	case int64:
		// Unix时间戳
		if v > 1e10 {
			return time.Unix(v/1000, (v%1000)*1e6), nil
		}
		return time.Unix(v, 0), nil
	case float64:
		// Unix时间戳
		timestamp := int64(v)
		if timestamp > 1e10 {
			return time.Unix(timestamp/1000, (timestamp%1000)*1e6), nil
		}
		return time.Unix(timestamp, 0), nil
	case map[string]interface{}:
		// 处理对象，尝试获取value字段
		if val, exists := v["value"]; exists {
			return t.convertToTime(val)
		}
		return time.Time{}, fmt.Errorf("对象中未找到时间值")
	default:
		return time.Time{}, fmt.Errorf("不支持的时间类型: %T", v)
	}
}

// loadDefaultMappingRules 加载默认映射规则
func (t *ContractTransformer) loadDefaultMappingRules() {
	// 默认通用映射规则
	defaultRule := &FieldMappingRule{
		ApprovalCode: "default",
		ApprovalName: "通用合同审批",
		FieldMappings: map[string]FieldConfig{
			"application_number": {
				SourcePath:  "basic.instance_code",
				TargetField: "application_number",
				DataType:    "string",
				Required:    true,
				Description: "申请编号",
			},
			"title": {
				SourcePath:  "basic.approval_name",
				TargetField: "title",
				DataType:    "string",
				Required:    true,
				Description: "标题",
			},
			"application_status": {
				SourcePath:  "basic.status",
				TargetField: "application_status",
				DataType:    "string",
				Required:    true,
				Description: "申请状态",
			},
			"initiate_time": {
				SourcePath:  "basic.start_time",
				TargetField: "initiate_time",
				DataType:    "time",
				Required:    true,
				Description: "发起时间",
			},
			"complete_time": {
				SourcePath:  "basic.end_time",
				TargetField: "complete_time",
				DataType:    "time",
				Required:    false,
				Description: "完成时间",
			},
			"initiator_user_id": {
				SourcePath:  "basic.user_id",
				TargetField: "initiator_user_id",
				DataType:    "string",
				Required:    true,
				Description: "发起人User ID",
			},
			"initiator_department_id": {
				SourcePath:  "basic.department_id",
				TargetField: "initiator_department_id",
				DataType:    "string",
				Required:    false,
				Description: "发起人部门ID",
			},
			"serial_number": {
				SourcePath:  "basic.serial_number",
				TargetField: "serial_number",
				DataType:    "string",
				Required:    false,
				Description: "流水号",
			},
		},
		DefaultValues: map[string]interface{}{
			"data_version": "1.0",
		},
		RequiredFields: []string{
			"application_number",
			"title",
			"application_status",
			"initiate_time",
			"initiator_user_id",
		},
	}

	t.mappingRules["default"] = defaultRule

	// 销售合同审批映射规则
	salesContractRule := &FieldMappingRule{
		ApprovalCode: "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
		ApprovalName: "销售合同审批",
		FieldMappings: map[string]FieldConfig{
			// 继承默认规则
			"application_number": defaultRule.FieldMappings["application_number"],
			"title":              defaultRule.FieldMappings["title"],
			"application_status": defaultRule.FieldMappings["application_status"],
			"initiate_time":      defaultRule.FieldMappings["initiate_time"],
			"complete_time":      defaultRule.FieldMappings["complete_time"],
			"initiator_user_id":  defaultRule.FieldMappings["initiator_user_id"],
			"serial_number":      defaultRule.FieldMappings["serial_number"],

			// 销售合同特有字段
			"payment_reason": {
				SourcePath:  "form.field_001.value",
				TargetField: "payment_reason",
				DataType:    "string",
				Required:    true,
				Description: "付款事由",
			},
			"payment_entity": {
				SourcePath:  "form.field_002.value",
				TargetField: "payment_entity",
				DataType:    "string",
				Required:    true,
				Description: "付款主体",
			},
			"contract_sign_amount": {
				SourcePath:  "form.field_003.value",
				TargetField: "contract_sign_amount",
				DataType:    "float",
				Required:    true,
				Description: "合同签约金额",
			},
			"current_request_amount": {
				SourcePath:  "form.field_004.value",
				TargetField: "current_request_amount",
				DataType:    "float",
				Required:    true,
				Description: "本次请款金额",
			},
			"bank_name": {
				SourcePath:  "form.field_005.value",
				TargetField: "bank_name",
				DataType:    "string",
				Required:    false,
				Description: "银行名称",
			},
			"account_holder": {
				SourcePath:  "form.field_006.value",
				TargetField: "account_holder",
				DataType:    "string",
				Required:    false,
				Description: "账户户名",
			},
			"account_number": {
				SourcePath:  "form.field_007.value",
				TargetField: "account_number",
				DataType:    "string",
				Required:    false,
				Description: "账户号码",
			},
		},
		DefaultValues: map[string]interface{}{
			"business_type": "销售合同",
			"data_version":  "1.0",
		},
		RequiredFields: []string{
			"application_number",
			"title",
			"payment_reason",
			"payment_entity",
			"contract_sign_amount",
			"current_request_amount",
		},
	}

	t.mappingRules["F523F053-7AC6-4280-A4E7-B35E0C0431B5"] = salesContractRule
}

// registerDefaultValidators 注册默认验证器
func (t *ContractTransformer) registerDefaultValidators() {
	// 必填字段验证器
	t.validators = append(t.validators, &RequiredFieldValidator{})

	// 数据格式验证器
	t.validators = append(t.validators, &DataFormatValidator{})

	// 业务逻辑验证器
	t.validators = append(t.validators, &BusinessLogicValidator{})
}

// registerDefaultProcessors 注册默认处理器
func (t *ContractTransformer) registerDefaultProcessors() {
	// 时间线处理器
	t.processors = append(t.processors, &TimelineProcessor{})

	// 汇率转换处理器
	t.processors = append(t.processors, &CurrencyProcessor{})

	// 审批流程处理器
	t.processors = append(t.processors, &ApprovalFlowProcessor{})
}

// RequiredFieldValidator 必填字段验证器
type RequiredFieldValidator struct{}

func (v *RequiredFieldValidator) Validate(data *StandardContractData) []ValidationError {
	var errors []ValidationError

	// 检查基础必填字段
	if data.ApplicationNumber == "" {
		errors = append(errors, ValidationError{
			Field:   "application_number",
			Message: "申请编号不能为空",
			Value:   data.ApplicationNumber,
		})
	}

	if data.Title == "" {
		errors = append(errors, ValidationError{
			Field:   "title",
			Message: "标题不能为空",
			Value:   data.Title,
		})
	}

	if data.ApplicationStatus == "" {
		errors = append(errors, ValidationError{
			Field:   "application_status",
			Message: "申请状态不能为空",
			Value:   data.ApplicationStatus,
		})
	}

	if data.InitiateTime.IsZero() {
		errors = append(errors, ValidationError{
			Field:   "initiate_time",
			Message: "发起时间不能为空",
			Value:   data.InitiateTime.String(),
		})
	}

	if data.InitiatorUserId == "" {
		errors = append(errors, ValidationError{
			Field:   "initiator_user_id",
			Message: "发起人User ID不能为空",
			Value:   data.InitiatorUserId,
		})
	}

	return errors
}

// DataFormatValidator 数据格式验证器
type DataFormatValidator struct{}

func (v *DataFormatValidator) Validate(data *StandardContractData) []ValidationError {
	var errors []ValidationError

	// 验证金额字段
	if data.ContractSignAmount < 0 {
		errors = append(errors, ValidationError{
			Field:   "contract_sign_amount",
			Message: "合同签约金额不能为负数",
			Value:   fmt.Sprintf("%.2f", data.ContractSignAmount),
		})
	}

	if data.CurrentRequestAmount < 0 {
		errors = append(errors, ValidationError{
			Field:   "current_request_amount",
			Message: "本次请款金额不能为负数",
			Value:   fmt.Sprintf("%.2f", data.CurrentRequestAmount),
		})
	}

	// 验证税率
	if data.TaxRate < 0 || data.TaxRate > 1 {
		errors = append(errors, ValidationError{
			Field:   "tax_rate",
			Message: "税率应在0-1之间",
			Value:   fmt.Sprintf("%.4f", data.TaxRate),
		})
	}

	return errors
}

// BusinessLogicValidator 业务逻辑验证器
type BusinessLogicValidator struct{}

func (v *BusinessLogicValidator) Validate(data *StandardContractData) []ValidationError {
	var errors []ValidationError

	// 验证请款金额不能超过合同金额
	if data.ContractSignAmount > 0 && data.CurrentRequestAmount > data.ContractSignAmount {
		errors = append(errors, ValidationError{
			Field:   "current_request_amount",
			Message: "本次请款金额不能超过合同签约金额",
			Value:   fmt.Sprintf("请款: %.2f, 合同: %.2f", data.CurrentRequestAmount, data.ContractSignAmount),
		})
	}

	// 验证完成时间不能早于发起时间
	if !data.CompleteTime.IsZero() && !data.InitiateTime.IsZero() && data.CompleteTime.Before(data.InitiateTime) {
		errors = append(errors, ValidationError{
			Field:   "complete_time",
			Message: "完成时间不能早于发起时间",
			Value:   fmt.Sprintf("完成: %s, 发起: %s", data.CompleteTime.Format("2006-01-02 15:04:05"), data.InitiateTime.Format("2006-01-02 15:04:05")),
		})
	}

	return errors
}

// TimelineProcessor 时间线处理器
type TimelineProcessor struct{}

func (p *TimelineProcessor) Process(data *StandardContractData) error {
	// 计算审批耗时
	if !data.InitiateTime.IsZero() && !data.CompleteTime.IsZero() {
		duration := data.CompleteTime.Sub(data.InitiateTime)
		data.ApprovalDuration = int64(duration.Minutes())
	}

	return nil
}

// CurrencyProcessor 汇率转换处理器
type CurrencyProcessor struct{}

func (p *CurrencyProcessor) Process(data *StandardContractData) error {
	// 这里可以添加汇率转换逻辑
	// 示例：将外币金额转换为人民币

	// 模拟汇率（实际应用中应该从汇率API获取）
	exchangeRates := map[string]float64{
		"USD": 7.2,  // 美元汇率
		"EUR": 7.8,  // 欧元汇率
		"JPY": 0.05, // 日元汇率
		"HKD": 0.92, // 港币汇率
		"GBP": 8.9,  // 英镑汇率
	}

	// 转换各币种金额为人民币
	if data.UsdAmount > 0 {
		data.UsdToRmbAmount = data.UsdAmount * exchangeRates["USD"]
	}
	if data.EurAmount > 0 {
		data.EurToRmbAmount = data.EurAmount * exchangeRates["EUR"]
	}
	if data.JpyAmount > 0 {
		data.JpyToRmbAmount = data.JpyAmount * exchangeRates["JPY"]
	}
	if data.HkdAmount > 0 {
		data.HkdToRmbAmount = data.HkdAmount * exchangeRates["HKD"]
	}
	if data.GbpAmount > 0 {
		data.GbpToRmbAmount = data.GbpAmount * exchangeRates["GBP"]
	}

	return nil
}

// ApprovalFlowProcessor 审批流程处理器
type ApprovalFlowProcessor struct{}

func (p *ApprovalFlowProcessor) Process(data *StandardContractData) error {
	// 这里可以添加审批流程相关的处理逻辑
	// 例如：解析时间线数据，提取审批人信息等

	// 示例：设置审批人数（实际应该从时间线数据中解析）
	if len(data.HistoryApprovers) > 0 {
		data.ApproverCount = len(data.HistoryApprovers)
	}

	return nil
}

// 自定义转换函数

// extractBankName 从银行信息JSON中提取银行名称
func (t *ContractTransformer) extractBankName(value interface{}) interface{} {
	if value == nil {
		return ""
	}

	str := t.convertToString(value)
	if str == "" {
		return ""
	}

	// 解析JSON字符串
	var bankInfo map[string]interface{}
	if err := json.Unmarshal([]byte(str), &bankInfo); err != nil {
		return str // 如果解析失败，返回原始值
	}

	// 提取中文银行名称
	if bankNameZh, exists := bankInfo["bankNameZh"]; exists {
		return t.convertToString(bankNameZh)
	}

	// 如果没有中文名称，尝试英文名称
	if bankNameEn, exists := bankInfo["bankNameEn"]; exists {
		return t.convertToString(bankNameEn)
	}

	return str
}

// extractBankBranch 从银行支行信息JSON中提取支行名称
func (t *ContractTransformer) extractBankBranch(value interface{}) interface{} {
	if value == nil {
		return ""
	}

	str := t.convertToString(value)
	if str == "" {
		return ""
	}

	// 解析JSON字符串
	var branchInfo map[string]interface{}
	if err := json.Unmarshal([]byte(str), &branchInfo); err != nil {
		return str // 如果解析失败，返回原始值
	}

	// 提取中文支行名称
	if branchNameZh, exists := branchInfo["bankBranchNameZh"]; exists {
		return t.convertToString(branchNameZh)
	}

	// 如果没有中文名称，尝试英文名称
	if branchNameEn, exists := branchInfo["bankBranchNameEn"]; exists {
		return t.convertToString(branchNameEn)
	}

	// 尝试获取完整名称
	if name, exists := branchInfo["name"]; exists {
		return t.convertToString(name)
	}

	return str
}

// extractBankRegion 从银行地区信息JSON中提取地区名称
func (t *ContractTransformer) extractBankRegion(value interface{}) interface{} {
	if value == nil {
		return ""
	}

	str := t.convertToString(value)
	if str == "" {
		return ""
	}

	// 解析JSON数组字符串
	var regions []map[string]interface{}
	if err := json.Unmarshal([]byte(str), &regions); err != nil {
		return str // 如果解析失败，返回原始值
	}

	// 提取地区名称，通常取最后一个（最具体的地区）
	var regionNames []string
	for _, region := range regions {
		if name, exists := region["name"]; exists {
			regionNames = append(regionNames, t.convertToString(name))
		}
	}

	if len(regionNames) > 0 {
		return strings.Join(regionNames, " ")
	}

	return str
}

// parseAttachments 解析附件信息
func (t *ContractTransformer) parseAttachments(value interface{}) interface{} {
	if value == nil {
		return []AttachmentInfo{}
	}

	str := t.convertToString(value)
	if str == "" {
		return []AttachmentInfo{}
	}

	// 按逗号分割附件名称
	attachments := strings.Split(str, ",")

	// 清理每个附件名称并创建结构体
	var cleanAttachments []AttachmentInfo
	for _, attachment := range attachments {
		cleaned := strings.TrimSpace(attachment)
		if cleaned != "" {
			cleanAttachments = append(cleanAttachments, AttachmentInfo{
				FileName: cleaned,
				FileURL:  "", // 原有逻辑只有文件名，没有URL
			})
		}
	}

	return cleanAttachments
}

// parseAttachmentsWithUrls 解析附件信息（包含文件名和URL）
func (t *ContractTransformer) parseAttachmentsWithUrls(value interface{}) interface{} {
	if value == nil {
		return []AttachmentInfo{}
	}

	// 期望的数据结构：
	// {
	//   "ext": "文件名1.pdf,文件名2.pdf",
	//   "value": ["http://url1", "http://url2"]
	// }

	var result []AttachmentInfo

	switch v := value.(type) {
	case map[string]interface{}:
		// 获取文件名列表
		var fileNames []string
		if ext, exists := v["ext"]; exists {
			extStr := t.convertToString(ext)
			if extStr != "" {
				names := strings.Split(extStr, ",")
				for _, name := range names {
					cleaned := strings.TrimSpace(name)
					if cleaned != "" {
						fileNames = append(fileNames, cleaned)
					}
				}
			}
		}

		// 获取URL列表
		var urls []string
		if valueField, exists := v["value"]; exists {
			switch urlValue := valueField.(type) {
			case []interface{}:
				for _, url := range urlValue {
					if urlStr := t.convertToString(url); urlStr != "" {
						urls = append(urls, urlStr)
					}
				}
			case []string:
				urls = urlValue
			case string:
				if urlValue != "" {
					urls = append(urls, urlValue)
				}
			}
		}

		// 组合文件名和URL
		maxLen := len(fileNames)
		if len(urls) > maxLen {
			maxLen = len(urls)
		}

		for i := 0; i < maxLen; i++ {
			var fileName, url string

			if i < len(fileNames) {
				fileName = fileNames[i]
			}
			if i < len(urls) {
				url = urls[i]
			}

			// 创建附件信息结构体
			if fileName != "" || url != "" {
				result = append(result, AttachmentInfo{
					FileName: fileName,
					FileURL:  url,
				})
			}
		}

	default:
		// 如果不是期望的结构，回退到原有逻辑
		return t.parseAttachments(value)
	}

	return result
}

// convertToAttachmentInfoArray 将值转换为附件信息数组
func (t *ContractTransformer) convertToAttachmentInfoArray(value interface{}) []AttachmentInfo {
	if value == nil {
		return []AttachmentInfo{}
	}

	var result []AttachmentInfo

	switch v := value.(type) {
	case []AttachmentInfo:
		// 如果已经是 AttachmentInfo 数组，直接返回
		return v
	case []interface{}:
		// 如果是 interface{} 数组，尝试转换每个元素
		for _, item := range v {
			if attachmentInfo := t.convertSingleToAttachmentInfo(item); attachmentInfo != nil {
				result = append(result, *attachmentInfo)
			}
		}
		return result
	case []string:
		// 如果是字符串数组，假设格式为 "文件名|URL"
		for _, str := range v {
			if attachmentInfo := t.parseAttachmentString(str); attachmentInfo != nil {
				result = append(result, *attachmentInfo)
			}
		}
		return result
	case string:
		// 如果是单个字符串，按逗号分割后处理
		if v == "" {
			return []AttachmentInfo{}
		}
		attachments := strings.Split(v, ",")
		for _, attachment := range attachments {
			cleaned := strings.TrimSpace(attachment)
			if cleaned != "" {
				if attachmentInfo := t.parseAttachmentString(cleaned); attachmentInfo != nil {
					result = append(result, *attachmentInfo)
				}
			}
		}
		return result
	default:
		// 其他类型，先转换为字符串再处理
		str := t.convertToString(value)
		return t.convertToAttachmentInfoArray(str)
	}
}

// convertSingleToAttachmentInfo 将单个值转换为附件信息
func (t *ContractTransformer) convertSingleToAttachmentInfo(value interface{}) *AttachmentInfo {
	switch v := value.(type) {
	case map[string]interface{}:
		// 如果是对象，尝试提取 file_name 和 file_url 字段
		fileName := t.convertToString(v["file_name"])
		fileURL := t.convertToString(v["file_url"])
		if fileName != "" || fileURL != "" {
			return &AttachmentInfo{
				FileName: fileName,
				FileURL:  fileURL,
			}
		}
	case string:
		// 如果是字符串，尝试解析
		return t.parseAttachmentString(v)
	}
	return nil
}

// parseAttachmentString 解析附件字符串
func (t *ContractTransformer) parseAttachmentString(str string) *AttachmentInfo {
	if str == "" {
		return nil
	}

	// 检查是否包含分隔符 "|"
	if strings.Contains(str, "|") {
		parts := strings.SplitN(str, "|", 2)
		return &AttachmentInfo{
			FileName: strings.TrimSpace(parts[0]),
			FileURL:  strings.TrimSpace(parts[1]),
		}
	}

	// 如果没有分隔符，判断是文件名还是URL
	if strings.HasPrefix(str, "http://") || strings.HasPrefix(str, "https://") {
		return &AttachmentInfo{
			FileName: "",
			FileURL:  str,
		}
	}

	// 默认作为文件名处理
	return &AttachmentInfo{
		FileName: str,
		FileURL:  "",
	}
}

// applyTransform 应用转换函数
func (t *ContractTransformer) applyTransform(value interface{}, transformName string) interface{} {
	switch transformName {
	case "parseAttachments":
		return t.parseAttachments(value)
	case "parseAttachmentsWithUrls":
		return t.parseAttachmentsWithUrls(value)
	case "extractBankName":
		return t.extractBankName(value)
	case "extractBankBranch":
		return t.extractBankBranch(value)
	case "extractBankRegion":
		return t.extractBankRegion(value)
	default:
		// 如果没有匹配的转换函数，返回原值
		return value
	}
}

// convertToStringArray 将值转换为字符串数组
func (t *ContractTransformer) convertToStringArray(value interface{}) []string {
	if value == nil {
		return []string{}
	}

	switch v := value.(type) {
	case []string:
		// 如果已经是字符串数组，直接返回
		return v
	case []interface{}:
		// 如果是 interface{} 数组，转换每个元素为字符串
		var result []string
		for _, item := range v {
			if str := t.convertToString(item); str != "" {
				result = append(result, str)
			}
		}
		return result
	case string:
		// 如果是字符串，按逗号分割
		if v == "" {
			return []string{}
		}
		attachments := strings.Split(v, ",")
		var result []string
		for _, attachment := range attachments {
			cleaned := strings.TrimSpace(attachment)
			if cleaned != "" {
				result = append(result, cleaned)
			}
		}
		return result
	default:
		// 其他类型，先转换为字符串再处理
		str := t.convertToString(value)
		if str == "" {
			return []string{}
		}
		attachments := strings.Split(str, ",")
		var result []string
		for _, attachment := range attachments {
			cleaned := strings.TrimSpace(attachment)
			if cleaned != "" {
				result = append(result, cleaned)
			}
		}
		return result
	}
}

// 以下方法用于测试目的，导出私有方法

// ParseAttachmentsWithUrls 导出的测试方法
func (t *ContractTransformer) ParseAttachmentsWithUrls(value interface{}) interface{} {
	return t.parseAttachmentsWithUrls(value)
}

// ParseAttachments 导出的测试方法
func (t *ContractTransformer) ParseAttachments(value interface{}) interface{} {
	return t.parseAttachments(value)
}

// ConvertToAttachmentInfoArray 导出的测试方法
func (t *ContractTransformer) ConvertToAttachmentInfoArray(value interface{}) []AttachmentInfo {
	return t.convertToAttachmentInfoArray(value)
}

// convertToExpenseDetailArray 将值转换为费用明细数组
func (t *ContractTransformer) convertToExpenseDetailArray(value interface{}) []ExpenseDetailItem {
	if value == nil {
		return []ExpenseDetailItem{}
	}

	// 如果已经是 ExpenseDetailItem 数组，直接返回
	if details, ok := value.([]ExpenseDetailItem); ok {
		return details
	}

	// 如果是 interface{} 数组，尝试转换
	if arr, ok := value.([]interface{}); ok {
		result := make([]ExpenseDetailItem, 0, len(arr))
		for i, item := range arr {
			if itemMap, ok := item.(map[string]interface{}); ok {
				detail := ExpenseDetailItem{
					RowIndex: i,
				}

				// 简单的字段映射
				if expenseType, exists := itemMap["expense_type"]; exists {
					detail.ExpenseType = t.convertToString(expenseType)
				}
				if location, exists := itemMap["location"]; exists {
					detail.Location = t.convertToString(location)
				}
				if dateRange, exists := itemMap["date_range"]; exists {
					detail.DateRange = t.convertToString(dateRange)
				}
				if vatType, exists := itemMap["vat_invoice_type"]; exists {
					detail.VatInvoiceType = t.convertToString(vatType)
				}
				if amount, exists := itemMap["amount"]; exists {
					detail.Amount = t.convertToFloat64(amount)
				}
				if currency, exists := itemMap["amount_currency"]; exists {
					detail.AmountCurrency = t.convertToString(currency)
				}
				if capital, exists := itemMap["amount_capital"]; exists {
					detail.AmountCapital = t.convertToString(capital)
				}

				result = append(result, detail)
			}
		}
		return result
	}

	return []ExpenseDetailItem{}
}

// processFieldList 处理 fieldList 类型字段
func (t *ContractTransformer) processFieldList(data *StandardContractData, targetField string, value interface{}, fieldConfig FieldConfig) error {
	// 将 value 转换为数组
	listData, ok := value.([]interface{})
	if !ok {
		return fmt.Errorf("fieldList 字段 %s 的值不是数组类型", targetField)
	}

	// 根据目标字段类型进行不同处理
	switch targetField {
	case "expense_details":
		return t.processExpenseDetailsList(data, listData, fieldConfig)
	default:
		// 通用 fieldList 处理
		return t.processGenericFieldList(data, targetField, listData, fieldConfig)
	}
}

// processExpenseDetailsList 处理费用明细列表
func (t *ContractTransformer) processExpenseDetailsList(data *StandardContractData, listData []interface{}, fieldConfig FieldConfig) error {
	expenseDetails := make([]ExpenseDetailItem, 0, len(listData))

	for i, item := range listData {
		// 尝试多种数据结构类型
		var itemMap map[string]interface{}
		var ok bool

		// 首先尝试直接转换为 map[string]interface{}
		if itemMap, ok = item.(map[string]interface{}); !ok {
			// 如果失败，尝试转换为 map[interface{}]interface{} 然后转换
			if interfaceMap, ok2 := item.(map[interface{}]interface{}); ok2 {
				itemMap = make(map[string]interface{})
				for k, v := range interfaceMap {
					if keyStr, ok3 := k.(string); ok3 {
						itemMap[keyStr] = v
					}
				}
			} else {
				// 记录调试信息并跳过无效项
				global.GVA_LOG.Warn("费用明细项类型不匹配",
					zap.Int("index", i),
					zap.String("type", fmt.Sprintf("%T", item)),
					zap.Any("value", item),
				)
				continue
			}
		}

		detail := ExpenseDetailItem{
			RowIndex: i,
		}

		// 记录调试信息
		global.GVA_LOG.Debug("处理费用明细项",
			zap.Int("index", i),
			zap.Any("itemMap", itemMap),
			zap.Int("fieldCount", len(fieldConfig.FieldListConfig)),
		)

		// 处理每个字段
		for fieldName, config := range fieldConfig.FieldListConfig {
			fieldValue, err := t.extractFieldValueFromMap(itemMap, config.SourcePath)
			if err != nil {
				global.GVA_LOG.Warn("提取费用明细字段失败",
					zap.Int("index", i),
					zap.String("fieldName", fieldName),
					zap.String("sourcePath", config.SourcePath),
					zap.Error(err),
				)
				if config.Required {
					return fmt.Errorf("提取费用明细第 %d 行字段 %s 失败: %w", i+1, fieldName, err)
				}
				continue
			}

			// 记录字段提取成功
			global.GVA_LOG.Debug("费用明细字段提取成功",
				zap.Int("index", i),
				zap.String("fieldName", fieldName),
				zap.Any("value", fieldValue),
			)

			// 设置到 detail 结构中
			if err := t.setExpenseDetailField(&detail, fieldName, fieldValue, config.DataType); err != nil {
				return fmt.Errorf("设置费用明细第 %d 行字段 %s 失败: %w", i+1, fieldName, err)
			}
		}

		expenseDetails = append(expenseDetails, detail)
	}

	data.ExpenseDetails = expenseDetails
	return nil
}

// processGenericFieldList 处理通用 fieldList 类型
func (t *ContractTransformer) processGenericFieldList(data *StandardContractData, targetField string, listData []interface{}, fieldConfig FieldConfig) error {
	// 这里可以实现通用的 fieldList 处理逻辑
	// 暂时只是将原始数据存储
	return t.setFieldValue(data, targetField, listData, "array")
}

// extractFieldValueFromMap 从 map 中提取字段值
func (t *ContractTransformer) extractFieldValueFromMap(data map[string]interface{}, sourcePath string) (interface{}, error) {
	if sourcePath == "" {
		return nil, fmt.Errorf("源路径为空")
	}

	// 记录调试信息
	global.GVA_LOG.Debug("提取字段值",
		zap.String("sourcePath", sourcePath),
		zap.Any("dataKeys", t.getMapKeys(data)),
	)

	// 分割路径
	parts := strings.Split(sourcePath, ".")
	current := data

	for i, part := range parts {
		if i == len(parts)-1 {
			// 最后一个部分，返回值
			value, exists := current[part]
			if !exists {
				// 尝试查找所有可能的键
				global.GVA_LOG.Debug("字段不存在，尝试模糊匹配",
					zap.String("targetKey", part),
					zap.Any("availableKeys", t.getMapKeys(current)),
				)
				return nil, fmt.Errorf("路径 %s 中的 %s 不存在", sourcePath, part)
			}
			return value, nil
		}

		// 中间部分，继续向下查找
		next, exists := current[part]
		if !exists {
			return nil, fmt.Errorf("路径 %s 中的 %s 不存在", sourcePath, part)
		}

		// 尝试转换为 map[string]interface{}
		nextMap, ok := next.(map[string]interface{})
		if !ok {
			// 尝试从 map[interface{}]interface{} 转换
			if interfaceMap, ok2 := next.(map[interface{}]interface{}); ok2 {
				nextMap = make(map[string]interface{})
				for k, v := range interfaceMap {
					if keyStr, ok3 := k.(string); ok3 {
						nextMap[keyStr] = v
					}
				}
			} else {
				return nil, fmt.Errorf("路径 %s 中的 %s 不是对象类型，实际类型: %T", sourcePath, part, next)
			}
		}

		current = nextMap
	}

	return nil, fmt.Errorf("无法提取路径 %s 的值", sourcePath)
}

// getMapKeys 获取 map 的所有键（用于调试）
func (t *ContractTransformer) getMapKeys(data map[string]interface{}) []string {
	keys := make([]string, 0, len(data))
	for k := range data {
		keys = append(keys, k)
	}
	return keys
}

// setExpenseDetailField 设置费用明细字段值
func (t *ContractTransformer) setExpenseDetailField(detail *ExpenseDetailItem, fieldName string, value interface{}, dataType string) error {
	if value == nil {
		return nil // 空值跳过
	}

	switch fieldName {
	case "expense_type":
		detail.ExpenseType = t.convertToString(value)
	case "location":
		detail.Location = t.convertToString(value)
	case "date_range":
		detail.DateRange = t.convertToString(value)
	case "start_date":
		if dataType == "time" {
			if timeVal, err := t.convertToTime(value); err == nil {
				detail.StartDate = timeVal
			}
		}
	case "end_date":
		if dataType == "time" {
			if timeVal, err := t.convertToTime(value); err == nil {
				detail.EndDate = timeVal
			}
		}
	case "vat_invoice_type":
		detail.VatInvoiceType = t.convertToString(value)
	case "amount":
		detail.Amount = t.convertToFloat64(value)
	case "amount_currency":
		detail.AmountCurrency = t.convertToString(value)
	case "amount_capital":
		detail.AmountCapital = t.convertToString(value)
	default:
		return fmt.Errorf("未知的费用明细字段: %s", fieldName)
	}

	return nil
}
